import Vue from "vue";
import Router from "vue-router";

import { Message } from 'element-ui';

import menus from "@/config/menu-config";

import LoginHelper from "@/utils/LoginHelper";

import Main from "@/components/pages/main.vue";
import Home from "@/components/Home.vue";
import Login from "@/components/Login.vue";
import UserInfo from "@/components/pages/userInfo.vue";
import ChangePassword from "@/components/pages/changePassword.vue";
import UnloginHome from "@/components/pages/unloginMain.vue";
import ResetPassword from "@/components/pages/resetPassword.vue";

Vue.use(Router);
//路由
var mainRouters = [],  moduleRouters = [];
//
var rootRouter = {path: "/", component: Login};
//初次访问首页
var unloginRouter = {path:"/",name:'页', component: UnloginHome};
//
var homeRouter = {path: "/home", name: "主页", component: Home};
//
var loginRouter = {path: "/login", name: "登录", component: Login};
//
var resetPasswordRouter = {path: "/resetPassword", name: "重置密码", component: ResetPassword};
//一级路由
mainRouters.push(
  // rootRouter
  // , 
  unloginRouter
  , homeRouter
  , loginRouter
  , resetPasswordRouter
);
//
var mainRouter = {path: "/main", name: "首页", component: Main};
moduleRouters.push(mainRouter);
//必须路由用户信息
var userInfoRouter = {
  path: "/userInfo",
  name: "个人中心",
  component: UserInfo
};
moduleRouters.push(userInfoRouter);
//必须路由修改密码
var changePasswordRouter = {
  path: "/changePassword",
  name: "修改密码",
  component: ChangePassword
};
moduleRouters.push(changePasswordRouter);



//二级路由, 菜单, 文件放在"/component/pages"下
menus.forEach(item => {
  item.sub.forEach(sub => {
    var path = "/" + sub.componentName;
    if(userInfoRouter.path == path || changePasswordRouter.path == path) {
      return true;
    }

    moduleRouters.push({
      path: path,
      name: sub.name,
      component: () => import(`@/components/pages/${sub.componentName}`),
      meta: sub.meta,
    });
  });
});

//二级路由
homeRouter["children"] = moduleRouters;
//
unloginRouter["children"] = [
  {
    path: "/platformIndex",
    name: "平台首页",
    component: () => import(`@/components/platform/PlatformIndex`)
  }, {
    path: "/platformRegisterGuide",
    name: "注册须知",
    component: () => import(`@/components/platform/PlatformRegisterGuide`)
  }, {
    path: "/platformRegisterUser",
    name: "用户信息填写",
    component: () => import(`@/components/platform/PlatformRegisterUser`)
  }, {
    path: "/platformRegisterOrg",
    name: "医院信息填写",
    component: () => import(`@/components/platform/PlatformRegisterOrg`)
  }
]

var router = new Router({
  base: "/",
  //mode: 'history',
  routes: mainRouters
});
//路由触发前执行, 用户登录认证
//路由首页测试 lf 20190703
router.beforeEach((to, from, next) => {
  var toPath = to.path;

  if (LoginHelper.requireAuth(to.name, to.path)) {
    Message({message: '登录超时或未登录.', type: 'warning'});
	  //
    return next(loginRouter);
  }

  return next();
});

export default router;
