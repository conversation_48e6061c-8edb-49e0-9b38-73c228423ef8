import PlatformRegisterSteps from "@/components/platform/PlatformRegisterSteps.vue";
import userInfo from "@/components/pages/userInfo.vue";

import formConfig from "@/config/uploadLog-config";
formConfig.init();

export default {
  
  components: {
    vRegisterSteps: PlatformRegisterSteps,
    vUserInfo: userInfo
  },

  data () {
    return {
      processing: false
    }
  },

  methods: {
    confirmTodo: function() {
      var vm = this;
      
      vm.$confirm('是否确定?', '确认').then(() => {
        vm.submitAndContinue();
      }).catch(() => {});
    },

    submitAndContinue: function(route) {
      var vm = this, ui = vm.$refs["ui"];
      ui.saveData({successHandler: vm.onSuccesSubmit});
    },

    onSuccesSubmit: function(res, ui) {
      if(!!res) {
        var vm = this;

        if("false" == res["result"]) {
          vm.$alert(res["message"]);
          return;
        }

        vm.$alert("您的信息已成功提交, 请选择完善医院信息.", {
          callback: function() {

            var route = {
              name: "医院信息填写"
              , path: "/platformRegisterOrg"
              , params: {
                "user.userCode": (ui["userCode"] || ui["mobile"])
                , "organization.id": (ui["corpCode"] || ui["corpCode_"])
              }
            };

            vm.$router.push(route);

          }
        });
      }
    }
  }
};
