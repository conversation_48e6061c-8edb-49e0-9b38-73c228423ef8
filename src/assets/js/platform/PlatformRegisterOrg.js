import PlatformRegisterSteps from "@/components/platform/PlatformRegisterSteps.vue";
import userOrganization from "@/components/pages/userOrganization.vue";

import formConfig from "@/config/uploadLog-config";
formConfig.init();

export default {
  
  components: {
    vRegisterSteps: PlatformRegisterSteps,
    vUserOrganization: userOrganization
  },

  data () {
    return {
      indexRoute: { name: "平台首页" },
      processing: false,
      editable: false
    }
  },

  created: function() {
    var vm = this, params = vm.$route.params;
    vm.editable = !!params && !!params["organization.id"] && !!params["user.userCode"];
  },

  methods: {
    confirmTodo: function() {

      var vm = this, uo = vm.$refs["uo"];
      uo.confirmToDo("submit", {successHandler: vm.onSuccesSubmit});
      
    },

    onSuccesSubmit: function(res, ui) {
      if(!!res && "true" == res["result"]) {
        var vm = this;
        vm.editable = false;
        
        //vm.$alert("您的申请已成功提交, 请等待审核, 审核结果将通过手机短信通知.");
        
        vm.$alert('您的申请已成功提交, 请等待审核, 审核结果将通过手机短信通知.',
          {
            callback: action => {
            vm.$router.push(vm.indexRoute);
          }
      });
      }
    }
  }
};