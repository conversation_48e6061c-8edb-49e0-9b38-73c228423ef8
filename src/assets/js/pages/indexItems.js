import global from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";
import AjaxUtil from "@/utils/AjaxUtil";
import LoginHelper from "@/utils/LoginHelper";
import StringUtil from "@/utils/StringUtil";
import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_indexItems",

  props: ["fatherData"],
  data() {
    return {
      enableEdit: false,
      underApprove: false,
      enableApprove: false,
      loading: false,
      formDisabled: true,
      formDisabledUpload: true,
      showPaneHead: true,
      isHistory: false,
      //
      table: {
        data: []
      },
      commentIdxs: [],
      lastHoverRow: null, // 记录最后一次鼠标悬停的行

      //
      checkData: [],

      tabs: {
        singleTask: "primary", //缺省上报任务
        multipleTask: ""
      },
      isMultipleTask: false, // 历史数据多个医院病区批量上传的

      form: {
        visible: true,
        validateFailMessage:
          '请设置所有条件："年份", "季度","季度","科室病区".',
        combo_year: formConfig.combo_year,
        combo_quarter: formConfig.combo_quarter,
        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_jobType: formConfig.combo_jobType,
        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),

        // combo_jobType: [
        //   { value: "1", label: "敏感指标任务" },
        //   { value: "4", label: "不良事件任务" },
        //   { value: "5", label: "优质护理任务" },
        //   { value: "3", label: "档案任务" }
        // ],
        //任务ID
        value_id: "",
        value_year: "",
        value_quarter: "",
        value_dataFromTime: "",
        value_fromTime: "",
        value_orgId: [""],
        value_jobType: "1", //任务类型默认为敏感数据
        value_jobPeriod: "4", //任务周期默认为季度
        value_orgPartId: [""],
        value_enteredAt: "",
        value_reportTemplate: formConfig.value_user.hasOwnProperty(
          "orgPart_template_id"
        )
          ? formConfig.value_user.orgPart_template_id
          : 1
      },
      formRules: {
        value_year: [{required: true, message: "请选择年份"}],
        value_quarter: [{required: true, message: "请选择季度"}],
        value_orgPartId: [{required: true, message: "请选择科室病区"}]
      },

      approveForm: {
        visible: false,
        //combo_checkState: formConfig.combo_checkState_check,
        combo_checkState: [
          {value: "2", label: "通过"},
          {value: "3", label: "返修"}
        ],

        value_checkState: "",
        value_checkFeedback: "",

        user_avatar: global.cmis.domain + "/static/images/user1.jpg"
      },
      //上传文件时，携带的参数
      uploadData: {
        reportResultId: "",
        enteredAt: "",
        orgId: "",
        orgPartId: "",
        fromTime: "",
        dataPeriod: "",
        enteredOn: "",
        enteredBy: "",
        itemTypeName: "",
        statisticId: "",
        token: ""
      },
      uploadAction: global.platform.indexUploadAction,

      autoCalcItems: null,

      editForm: {},
      validator: {
        tableId: "e719b8a1-2447-11e9-9606-005056a12216",
        tableName: "d_nurseqcindex",

        timer: null,
        result: {},
        numErrors: 0,
        numWarnings: 0,
      }
    };
  },
  created: function () {
    var self = this;

    //如果作为别人的子组件则隐藏头部标题
    if (null != self.fatherData) {
      self.showPaneHead = self.fatherData;
    }
    //初始化 单位、病区
    self.form["value_orgPartId"] = StringUtil.getIdsById(
      formConfig.value_orgPartId,
      formConfig.value_user.orgPart_id
    );
    //入参解析
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);
    //console.log("indexItems.vue| ");
    //console.log("indexItems.formValues");
    //console.log(formValues);
    var fromTimeParam = formValues["toTime"];
    self.form.value_dataFromTime = fromTimeParam;

    self.form.value_fromTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(new Date(fromTimeParam)),
      "yyyy-MM-dd"
    );

    self.form.value_id = formValues["id"] || "";
    self.enableEdit = "edit" === formValues["mode"]; //不再用, 用流程来判定是否允许编辑
    self.formDisabledUpload = !self.enableEdit;
    //如果是历史上报页面就可以编辑
    if ("ture" === formValues["history"]) {
      self.isHistory = true;
      self.formDisabled = false;
      self.formDisabledUpload = false;
    }
    // value_year、value_quarter不再使用，改为一个字符串的 self.form.value_fromTime
    // var fromTime = fromTimeParam;
    // if (!fromTime && !self.form.value_id) {
    //   fromTime = DateUtil.dateAdd("q", -1, new Date());
    // }
    // if (fromTime) {
    //   self.form.value_year = fromTime.getFullYear() + "";
    //   self.form.value_quarter = Math.ceil((fromTime.getMonth() + 1) / 3) + "";
    // }

    // if (null == formValues["orgPartId"] ) {
    //   self.form.value_orgPartId = StringUtil.getIdsById(
    //     formConfig.value_orgPartId,
    //     "1"
    //   );
    //   //self.form.value_orgPartId = [formConfig.value_user.orgPart_id + ""];
    // } else {
    //   self.form.value_orgPartId = StringUtil.getIdsById(
    //     formConfig.value_orgPartId,
    //     formValues["orgPartId"]
    //   );
    // }
    if (null != formValues["orgPartId"]) {
      self.form.value_orgPartId = StringUtil.getIdsById(
        formConfig.value_orgPartId,
        formValues["orgPartId"]
      );
      //self.form.value_orgPartId = [formConfig.value_user.orgPart_id + ""];
      self.changeOrgpart(self.form.value_orgPartId);
      self.$emit("setTemplate", self.form.value_reportTemplate);
    }

    self.form.value_enteredAt = formConfig.value_user.org_id + ""; //缺省机构id

    if (null == formValues["orgId"]) {
      self.form.value_orgId = StringUtil.getIdsById(
        formConfig.value_orgId,
        formConfig.value_user.org_id
      );
    } else {
      self.form.value_orgId = StringUtil.getIdsById(
        formConfig.value_orgId,
        formValues["orgId"]
      );
    }

    self.form.value_jobType = formValues["jobType"] + "";
    // alert(self.form.value_jobType);
    // alert(formConfig.value_jobType);
    // console.log("=======" + formConfig.value_jobType);
    self.form.value_jobPeriod =
      formConfig.value_jobType[self.form.value_jobType].period;

    self.getTableData();
  },
  methods: {
    downLoad() {
      window.open("static/template/填报模板.rar");
    },
    changeOrgpart: function (labels) {
      var self = this;

      if (labels.length <= 0) return;

      var selectId = labels[labels.length - 1];
      self.form.value_reportTemplate =
        formConfig.value_orgPartId[selectId].templateId;
    },
    changeState: function (label) {
      var self = this;
      if (label == "2") {
        self.approveForm.value_checkFeedback = "通过.";
      } else {
        self.approveForm.value_checkFeedback = "";
      }
    },
    commentSpanMethod({row, column, rowIndex, columnIndex}) {
      var self = this;
      var re = /\./;
      if (columnIndex != 3) {
        return;
      }

      //console.log("commentSpanMethod - rowIndex:", rowIndex, "row.name:", row.name, "row.code:", row.code);

      if (rowIndex == 0) {
        self.table.data[rowIndex].comIdx = 0;
        //console.log("设置第一行comIdx = 0");
        return;
      }

      //一级标题直接返回
      if (!re.test(self.table.data[rowIndex].code)) {
        self.table.data[rowIndex].comIdx = rowIndex;
        //console.log("一级标题，设置comIdx =", rowIndex);
        return;
      }
      //上一行不是是一级标题返回
      if (re.test(self.table.data[rowIndex - 1].code)) {
        //console.log("上一行是二级标题，返回");
        return;
      }

      //下一行是一级标题返回且上一行不是一级标题
      //
      if (
        rowIndex + 1 < self.table.data.length &&
        !re.test(self.table.data[rowIndex + 1].code)
      ) {
        if (!re.test(self.table.data[rowIndex - 1].code)) {
          self.table.data[rowIndex].comIdx = rowIndex;
          //console.log("特殊情况，设置comIdx =", rowIndex);
        }
        return;
      }

      //self.table.data[rowIdx].comments = self.table.data[rowIdx].description;
      //从每个一级标题下的第一个二级标题开始进行行合并
      var combineRow = 0;
      //console.log("开始行合并，起始rowIndex:", rowIndex);
      for (var idx = rowIndex; idx < self.table.data.length; idx++) {
        //遇到一级标题后返回
        if (!re.test(self.table.data[idx].code)) break;

        self.table.data[idx].comIdx = rowIndex;
        self.table.data[idx].name; //添加这行之后才会触发注释更新
        //console.log("设置行", idx, "的comIdx =", rowIndex, "name:", self.table.data[idx].name);
        combineRow++;
      }
      //console.log("行合并完成，combineRow:", combineRow);

      return {
        rowspan: combineRow,
        colspan: 1
      };
    },
    handleRowEnter: function (row, column, cell, event) {
      var self = this;
      /*console.log("=== handleRowEnter 开始 ===");
      console.log("row.name:", row.name);
      console.log("row.description:", row.description);
      console.log("row.comIdx:", row.comIdx);*/

      if (typeof self.table.data == "undefined") {
        console.log("table.data 未定义，返回");
        return;
      }

      var nowTime = new Date().getTime();
      // 只对同一行进行时间限制，不同行可以立即更新
      if (self.lastHoverRow === row && nowTime - row.updateTime < 1000) {
        //console.log("同一行时间限制，跳过更新");
        return;
      }

      //console.log("上一次hover行:", self.lastHoverRow ? self.lastHoverRow.name : "无");
      //console.log("当前hover行:", row.name);

      // 清空上一行的注释
      if (self.lastHoverRow && self.lastHoverRow !== row) {
        var lastComIdx = self.lastHoverRow.comIdx;
        //console.log("清空上一行注释，lastComIdx:", lastComIdx);
        if (lastComIdx !== undefined && self.table.data[lastComIdx]) {
          self.table.data[lastComIdx].comments = "";

          // 强制Vue更新表格
          var lastTargetRow = self.table.data[lastComIdx];
          var re = / /;
          if (re.test(lastTargetRow.name)) {
            lastTargetRow.name = lastTargetRow.name.replace(re, "");
          } else {
            lastTargetRow.name = lastTargetRow.name + " ";
          }
          //console.log("已清空上一行注释并强制刷新");
        }
      }

      row.updateTime = nowTime;
      row.isChange = true;
      self.lastHoverRow = row;

      var comIdx = row.comIdx;
      //console.log("设置注释 comIdx:", comIdx);
      //console.log("设置注释内容:", row.description);

      if (comIdx !== undefined && self.table.data[comIdx]) {
        self.table.data[comIdx].comments = row.description;
        //console.log("注释设置完成，当前comments:", self.table.data[comIdx].comments);
      } else {
        console.error("comIdx无效或对应数据不存在:", comIdx);
      }

      //在name增加空格和删除空格，保证表格刷新
      var re = / /;
      if (re.test(row.name)) row.name = row.name.replace(re, "");
      else row.name = row.name + " ";

      //console.log("=== handleRowEnter 结束 ===");
    },
    handleRowLeave: function (row, column, cell, event) {
      //row.comments = "";
      var self = this;
      /*console.log("=== handleRowLeave 开始 ===");
      console.log("row.name:", row.name);
      console.log("row.isChange:", row.isChange);*/

      if (typeof self.table.data == "undefined") {
        //console.log("table.data 未定义，返回");
        return;
      }
      if (row.isChange == false) {
        //console.log("row.isChange为false，返回");
        return;
      }

      //console.log("清空注释 comIdx:", row.comIdx);
      if (row.comIdx !== undefined && self.table.data[row.comIdx]) {
        self.table.data[row.comIdx].comments = "";
        //console.log("注释已清空");

        // 强制Vue更新表格，和handleRowEnter中一样的机制
        var targetRow = self.table.data[row.comIdx];
        var re = / /;
        if (re.test(targetRow.name)) {
          targetRow.name = targetRow.name.replace(re, "");
        } else {
          targetRow.name = targetRow.name + " ";
        }
        //console.log("已强制刷新表格");
      }
      row.isChange = false;

      // 清空当前hover行的引用
      if (self.lastHoverRow === row) {
        self.lastHoverRow = null;
        //console.log("清空lastHoverRow引用");
      }
      //console.log("=== handleRowLeave 结束 ===");
    },
    handleOnClick: function (row, column, cell, event) {
      var self = this;
      /*console.log("=== handleOnClick 开始 ===");
      console.log("row.name:", row.name);
      console.log("row.description:", row.description);*/

      if (typeof self.table.data == "undefined") {
        console.log("table.data 未定义，返回");
        return;
      }

      var comIdx = row.comIdx;
      //console.log("点击设置注释 comIdx:", comIdx);
      //console.log("点击设置注释内容:", row.description);

      if (comIdx !== undefined && self.table.data[comIdx]) {
        self.table.data[comIdx].comments = row.description;
        //console.log("点击注释设置完成，当前comments:", self.table.data[comIdx].comments);
      } else {
        console.error("点击时comIdx无效或对应数据不存在:", comIdx);
      }

      //在name增加空格和删除空格，保证表格刷新
      var re = / /;
      if (re.test(row.name)) row.name = row.name.replace(re, "");
      else row.name = row.name + " ";

      //console.log("=== handleOnClick 结束 ===");
    },
    getTableData: function () {
      var self = this;
      // console.log("getTableData");
      // console.log(self.form);
      //var vd = self.$refs['mainForm'].validate();
      //console.log(vd);
      if (!self.validateForm()) {
        var errM = self.form.validateFailMessage;
        self.$message({message: errM, type: "error"});
        return;
      }

      var usercode;
      if (!self.form.value_id) {
        var userInfo = LoginHelper.logincheck(),
          usercode = userInfo ? userInfo.usercode : null;
      }
      if (1 !== formConfig.value_user.org_id) {
        self.enableEdit = self.enableApprove = false;
        if (!self.isHistory) {
          self.formDisabledUpload = !self.enableEdit;
        }
      }
      //按jobType赋值任务时间数据时间
      var dataFromTime;
      if ("4" === self.form.value_jobPeriod) {
        dataFromTime = self.form.value_fromTime;
      } else {
        //数据时间 转换赋值
        dataFromTime = DateUtil.dateToStr(
          self.form.value_dataFromTime,
          "yyyy-MM-dd"
        );
      }

      //
      self.loading = true;
      //请求数据
      AjaxUtil.send({
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        traditional: true,
        type: "post",
        url: global.cmis.domain + "/carePlatform/index/getItemsList",
        data: {
          id: self.form.value_id,
          reportResultIds: [self.form.value_id],
          // year: self.form.value_year,
          // quarter: self.form.value_quarter,
          fromTime: dataFromTime,
          toTime: dataFromTime,
          //"orgPart.extend.extendI1": self.form.value_orgPartId
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          enteredAt: StringUtil.getLastNode(self.form.value_orgId),
          orgId: StringUtil.getLastNode(self.form.value_orgId),
          usercode: usercode,

          itemTypeName: "原始数据",
          isLeftJoin: "1",
          jobType: self.form.value_jobType
        },
        dataType: "json",
        success: function (res) {
          self.loading = false;
          if (!res || !res.result || "false" == res.result) {
            self.$alert(res.message);
            return;
          }

          self.table.data = res.data;
          // 重置hover状态
          self.lastHoverRow = null;

          console.log("=== 表格数据初始化 ===");
          console.log("表格数据长度:", self.table.data.length);

          var re = /\./;
          var nowTime = new Date().getTime();
          for (var i = 0; i < self.table.data.length; i++) {
            //一级标题下一行显示注释
            if (
              !re.test(self.table.data[i].code) &&
              i + 1 < self.table.data.length
            ) {
              self.commentIdxs.push(i + 1);
            }
            self.table.data[i].comments = "";
            self.table.data[i].isChange = false;
            self.table.data[i].updateTime = nowTime;

            // 打印每行数据的基本信息
            console.log("第", i, "行 - name:", self.table.data[i].name,
                       "code:", self.table.data[i].code,
                       "description:", self.table.data[i].description ? self.table.data[i].description.substring(0, 50) + "..." : "无");
          }

          console.log("commentIdxs:", self.commentIdxs);
          console.log("=== 表格数据初始化完成 ===");
          var checkFlow = res["checkFlow"];
          //更新父组件流程
          self.$emit("setCheckFlow", checkFlow);
          if (
            checkFlow &&
            checkFlow["nextNodeId"] &&
            checkFlow["nextCheckUsersId"]
          ) {
            var nextNodeId = parseInt(checkFlow["nextNodeId"]);
            self.enableEdit = nextNodeId === 1; //只要流程还处在待提交时刻，就给他编辑权限？
            self.enableApprove = nextNodeId > 1;
            if (!self.isHistory) {
              self.formDisabledUpload = !self.enableEdit;
            }
          }
          //
          if (res && res.length) {
            self.form.value_orgPartId = res[0]["organizationId"];
          }
          //存在审核记录, 显示审核弹框按钮
          self.underApprove = !!checkFlow && !!checkFlow["lastNodeId"];

          //是否可编辑
          self.enableEdit =
            !!self.form.value_id &&
            !!res["editable"] &&
            (!checkFlow ||
              (1 === checkFlow["nextNodeId"] &&
                !!checkFlow["nextCheckUsersId"]));
          //是否可上传,不为历史上传时候才判断流程的作用
          self.formDisabledUpload = !self.enableEdit;

          //是否允许审核意见
          self.enableApprove =
            checkFlow &&
            checkFlow["nextNodeId"] > 1 &&
            checkFlow["nextCheckUsersId"];

          if (checkFlow && 1 == checkFlow["isNationIndexLock"]) {
            self.formDisabledUpload = true;
          }
          //为历史上传时，都可以编辑
          if (self.isHistory) {
            self.formDisabledUpload = false;
            self.enableEdit = true;
          }

          //读取前端计算的指标
          if (res.data && res.data.length) {
            self.getAutoCalcItems();
          }
        },
        error: function () {
          self.loading = false;

          self.$alert("加载失败");
        }
      });
    },
    onSearch: function () {
      this.getTableData();
    },
    confirmToSaveData: function (cmd) {
      var self = this;

      self
        .$confirm("是否确定?", "确认")
        .then(() => {
          self.saveData(cmd);
        })
        .catch(() => {
        });
    },
    saveData: function (cmd) {
      var self = this;
      //console.log("save1");
      //console.log(self.form);

      if (!self.validateForm()) {
        var errM = self.form.validateFailMessage;
        //self.$message({message: errM, type: 'warning'});
        self.$alert(errM);
      }
      //表格的数据
      var tableData = self.table.data;
      //提交的表单数据
      var formData = {};
      //指标属性名
      var rowCellsName = [
        "id",
        "code",
        "name",
        "value",
        "calcObjId",
        "statisticId",
        "organizationId",
        "orgPartId"
      ];
      //可编辑指标数
      var numItemsEditable = 0,
        numItemsValid = 0;
      for (var i = 0, row; i < tableData.length; i++) {
        row = tableData[i];
        //不可编辑指标
        if (
          "2" == row["editType"] &&
          !("3" == row["produceMode"] && 1 == row["isCalc"])
        ) {
          continue;
        }
        //指标属性值
        for (var j = 0; j < rowCellsName.length; j++) {
          formData["items[" + numItemsEditable + "]." + rowCellsName[j]] =
            row[rowCellsName[j]];
        }
        //应填指标数
        var val = row["value"];
        if (val || (!val && !isNaN(parseFloat(val)))) {
          numItemsValid++;
        }
        //
        numItemsEditable++;
      }

      //没有编辑的指标
      if (0 >= numItemsEditable) {
        self.$alert("没有可提交的数据.");
        return;
      }
      //提交
      var handlePost = function () {
        //当前用户
        var userInfo = LoginHelper.logincheck(),
          usercode = userInfo ? userInfo.usercode : null;
        //
        //按jobType赋值任务时间数据时间
        var dataFromTime;
        if ("4" == self.form.value_jobPeriod) {
          dataFromTime = self.form.value_fromTime;
        } else {
          //数据时间 转换赋值
          dataFromTime = DateUtil.dateToStr(self.form.value_dataFromTime);
        }

        formData["fromTime"] = dataFromTime;
        formData["reportResult.id"] = self.form.value_id;
        // formData["year"] = self.form.value_year;
        // formData["quarter"] = self.form.value_quarter;
        //, orgId: self.form.value_orgPartId
        formData["orgPartId"] = StringUtil.getLastNode(
          self.form.value_orgPartId
        );
        formData["orgId"] = StringUtil.getLastNode(self.form.value_orgId);
        formData["usercode"] = usercode;
        formData["cmd"] = cmd;

        var ajaxOpts = {
          url: global.cmis.domain + "/carePlatform/index/saveItems",
          type: "post",
          dataType: "json",
          data: formData,
          success: function (res) {
            //console.log(res);
            //提示信息
            self.$alert(res["message"]);
            //成功, 刷新表格
            if (res["result"]) {
              //if (self.form.value_id) {
              self.onSearch();
              // } else {
              //  self.$router.push({ path: "/uploadLog" });
              //}
            }
          },
          complete: function () {
            self.loading = false;
          }
        };
        self.loading = true;
        AjaxUtil.send(ajaxOpts);
      };

      //已填写的指标数少于可编辑的指标数
      if (numItemsEditable > numItemsValid) {
        self
          .$confirm(
            "未填指标" +
            (numItemsEditable - numItemsValid) +
            "项，应填指标" +
            numItemsEditable +
            "项，已填" +
            numItemsValid +
            "项",
            "未填指标" + (numItemsEditable - numItemsValid) + "项，是否继续？"
          )
          .then(() => {
            handlePost();
          })
          .catch(() => {
          });
        return;
      }

      handlePost();
    },

    validateForm: function () {
      var self = this;
      var res =
        self.form.value_dataFromTime &&
        self.form.value_fromTime &&
        self.form.value_orgPartId;
      /*if(!res) {
          var errM = self.form.validateFailMessage;
          //self.$message({message: errM, type: 'warning'});
          self.$alert(errM);
        }*/
      return res;
    },

    /**
     * 打开审核窗口, 预备审核
     */
    prepareApprove: function () {
      var self = this;

      self.approveForm.visible = true;

      self.findApprove();
    },

    /**
     * 查找审核记录
     */
    findApprove: function () {
      var self = this;

      var ajaxOpts = {
        url: global.cmis.domain + "/carePlatform/check/findList",
        dataType: "json",
        data: {"reportResult.id": self.form.value_id},
        success: function (res) {
          //console.log(res);
          //成功, 刷新列表
          if ("true" == res["result"]) {
            self.checkData = res["data"];
            //重置表单
            self.approveForm.value_checkState = "";
            self.approveForm.value_checkFeedback = "";
            //滚动到最新审批
            setTimeout(function () {
              var checkDataPane = document.getElementById("checkDataPane"); //$("#checkDataPane").get(0);
              if (checkDataPane) {
                checkDataPane.scrollTop = checkDataPane.scrollHeight;
              }
            }, 200);
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 提交审核意见
     */
    approve: function () {
      var self = this;
      var checkState = self.approveForm.value_checkState;
      var checkFeedback = self.approveForm.value_checkFeedback;
      if (!checkState || !checkFeedback) {
        self.$alert("请选择'审核状态'并填写'审核意见'.");
        return;
      }

      //
      var formData = {};
      formData["reportResult.id"] = self.form.value_id;
      formData["checkState"] = checkState;
      formData["checkFeedback"] = checkFeedback;

      var ajaxOpts = {
        url: global.cmis.domain + "/carePlatform/check/approve",
        type: "post",
        dataType: "json",
        data: formData,
        success: function (res) {
          //console.log(res);
          //提示信息
          var msg = res["message"];
          if (msg) {
            //self.$alert(msg);
            self.$alert(msg, "提交结果", {
              callback: function () {
                if ("true" == res["result"]) {
                  self.approveForm.visible = false;
                }
              }
            });
          }
          //成功, 刷新表格
          if ("true" == res["result"]) {
            //self.approveForm.visible = false;

            self.findApprove();

            self.getTableData();
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 显示/隐藏过滤条件
     */
    swapFormVisibility: function () {
      this.form.visible = !this.form.visible;
    },
    /**
     * 行样式
     */
    tableRowClassName: function (tr) {
      var row = tr["row"],
        code = row["code"];
      //编码没有"."的是第1级分类分组
      var cls = code && /[\\.]/.test(code) ? "" : "table-row-group";
      //广西护理敏感质量 就换一种背景色
      var name = row["name"];
      if (name && /广西护理敏感质量/.test(name)) {
        cls = "table-row-group-gx";
      }
      return cls;
    },
    uploadFile: function (file) {
      var self = this;
      console.log("正在上传文件", file);
      //console.log(self.uploadData);
      //console.log(self.uploadAction);
    },
    beforeUploadFile(file) {
      var self = this;
      // if (size > 10) {
      //   this.$confirm("文件大小不得超过500M", {
      //     confirmButtonText: "确定"
      //   });
      //   return false;
      // }

      if (self.isHistory) {
        self.uploadData.reportResultId = "1"; //"1"为历史数据上报;
      } else {
        self.uploadData.reportResultId = self.form.value_id;
      }
      self.uploadData.enteredAt = StringUtil.getLastNode(self.form.value_orgId); //"4";
      self.uploadData.orgId = StringUtil.getLastNode(self.form.value_orgId); //"4";
      self.uploadData.orgPartId = StringUtil.getLastNode(
        self.form.value_orgPartId
      ); //"1";
      //按jobType赋值任务时间数据时间
      var dataFromTime;
      if ("4" == self.form.value_jobPeriod) {
        dataFromTime = self.form.value_fromTime;
      } else {
        //数据时间 转换赋值
        dataFromTime = DateUtil.dateToStr(self.form.value_dataFromTime);
      }

      self.uploadData.fromTime = dataFromTime;
      //self.form.value_year +"-" +((self.form.value_quarter - 1) * 3 + 1) +"-01 00:00:00";

      self.uploadData.dataPeriod = "4";
      self.uploadData.enteredOn = DateUtil.dateToStr(new Date()); //"2020-01-01 15:00:00";
      //self.uploadData.enteredBy = self.form.userInfo.usercode; //"1"; userInfo未定义
      self.uploadData.itemTypeName = "原始数据";
      self.uploadData.statisticId = "1";
      self.uploadData.token = LoginHelper.validateToken();
      //console.log(self.uploadData);
      console.log("上传文件");
      console.log(self.uploadData);
    },
    handleSuccessUpload: function (response, file, fileList) {
      var self = this;
      self.loadingMask.close();

      var msg = "提交成功";
      // "上传完成(总数=" +
      // response.totalNum +
      // ", 成功=" +
      // (response.successNum | response.sucessNum) +
      // ", 失败=" +
      // response.failNum +
      // ")";
      self.$alert(msg, "上传结果", {
        callback: function () {
          //if (response["successNum"] > 0) {
          self.getTableData();
          //}
        }
      });
    },
    handleErrorUpload: function (err, file, fileList) {
      var self = this;
      self.loadingMask.close();

      self.$alert(err);
    },
    handleProgressUpload: function (event, file, fileList) {
      var self = this;

      self.loadingMask = this.$loading({
        lock: true,
        text: "正在处理....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
    },
    pushSingleTask() {
      var self = this;

      self.tabs.singleTask = "primary";
      self.tabs.multipleTask = "";
      self.isMultipleTask = false;
    },

    pushMultipleTask() {
      var self = this;
      self.tabs.singleTask = "";
      self.tabs.multipleTask = "primary";
      self.isMultipleTask = true;
    },

    /**
     * 读取前端计算指标
     */
    getAutoCalcItems: function () {
      var vm = this;

      var ajaxOpts = {
        url: global.cmis.domain + "/carePlatform/index/autoCalcItems",
        dataType: "json",
        success: function (res) {
          if ("true" != res["result"]) {
            if (res["msg"]) {
              vm.$alert(res["msg"]);
            }

            return;
          }

          var autoCalcItems = (vm.autoCalcItems = res["data"]);
          if (!autoCalcItems || !autoCalcItems.length) {
            return;
          }

          var i, item, calcResultId, calcFormula, calcFactor;
          for (i = 0; i < autoCalcItems.length; i++) {
            item = autoCalcItems[i];
            calcFactor = item["calcFactor"];
            calcResultId = item["calcResultId"];
            calcFormula = item["calcFormula"];

            vm.autoCalc(calcResultId, calcFormula, calcFactor);
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 预备自动计算
     */
    triggerAutoCalc: function (row) {
      var vm = this,
        tableData = vm.table.data,
        autoCalcItems = vm.autoCalcItems;
      //
      //vm.validateCellField();
      //
      if (
        !tableData ||
        !tableData.length ||
        !autoCalcItems ||
        !autoCalcItems.length
      ) {
        return;
      }
      //指标ID
      var i,
        item,
        calcObjId = row["calcObjId"];
      //计算结果的指标, 计算公式, 参与计算的指标
      var calcResultId, calcFormula, calcFactor;
      //找到结果指标
      for (i = 0; i < autoCalcItems.length; i++) {
        //格式: {"calcFormula":"sENur1+sENur2+sENur3+sENur4+sENur5","calcFactor":"70:sENur4,66:sENur2,72:sENur5,68:sENur3,40:sENur1","calcResultId":74,"dict_label":"期初学历护士总人数"}
        item = autoCalcItems[i];
        //格式: "70:sENur4,66:sENur2,72:sENur5,68:sENur3,40:sENur1"
        calcFactor = item["calcFactor"];
        if (
          !calcFactor ||
          -1 == ("," + calcFactor).indexOf("," + calcObjId + ":")
        ) {
          continue;
        }
        calcResultId = item["calcResultId"];
        calcFormula = item["calcFormula"];
        //
        vm.autoCalc(calcResultId, calcFormula, calcFactor);
      }
    },

    /**
     * 自动计算
     */
    autoCalc: function (calcResultId, calcFormula, calcFactor) {
      var vm = this,
        tableData = vm.table.data;
      //
      if (!tableData || !tableData.length) {
        return;
      }
      var i, item;
      //结果指标, 参数名
      var calcResult, calcFactorParam, calcObjId, pos, val;
      //
      calcFactor = "," + calcFactor;
      for (i = 0; i < tableData.length; i++) {
        item = tableData[i];
        calcObjId = item["calcObjId"];
        //结果指标
        if (calcResultId == calcObjId) {
          calcResult = item;
          continue;
        }
        //计算指标和指标数值
        if (-1 == (pos = calcFactor.indexOf("," + calcObjId + ":"))) {
          // || isNaN(val = parseFloat(item["value"]))
          continue;
        }
        //参数, 匹配",66:sENur2", 取"sENur2"
        if (!new RegExp("\\," + calcObjId + "\\:([^,:]+)").test(calcFactor)) {
          continue;
        }
        calcFactorParam = RegExp.$1;
        //指标值替换参数名
        val = parseFloat(item["value"]) || 0.0;
        calcFormula = calcFormula.replace(calcFactorParam, val);
      }
      //console.log(calcResult);
      //console.log(calcFormula);
      //执行公式算结果
      if (calcResult) {
        try {
          val = eval(calcFormula);
          calcResult["value"] = val.toFixed(4);
        } catch (err) {
          console.error(err);
          console.error(calcFormula);
        }
      }
    },

    /**
     * 验证
     */
    validateCellField: function (rule, value, callback) {
      //console.log(rule);
      //
      var vm = this;

      if (vm.validator.timer) {
        clearTimeout(vm.validator.timer);
        vm.validator.timer = null;
      }

      vm.validator.timer = setTimeout(vm.validate, 400);
    },

    /**
     * 提交验证参数, 回调
     */
    validate: function (successCallback) {
      var vm = this, items = vm.table.data;
      vm.validator.result = {};
      vm.validator.numErrors = 0;
      vm.validator.numWarnings = 0;
      if (!items || !items.length) {
        return;
      }

      var postData = {
        "tableName": vm.validator.tableName,
        "tableId": vm.validator.tableId,
        "jobTypeId": vm.form.value_jobType,
      };
      //新填写指标, 时间取季度
      //按jobType赋值任务时间数据时间
      var defFromTime;
      if ("4" == vm.form.value_jobPeriod) {
        defFromTime = vm.form.value_fromTime;
      } else {
        //数据时间 转换赋值
        defFromTime = vm.form.value_dataFromTime;
      }
      if (!!defFromTime && (defFromTime instanceof Date)) {
        defFromTime = DateUtil.dateToStr(defFromTime);
      }

      //批量效验
      var i, row, idxId, val, fromTime, vi = 0;
      for (i = 0; i < items.length; i++) {
        row = items[i];
        idxId = row.calcObjId;
        val = row.value;

        if (!idxId || (!val && 0.0 != parseFloat(val))) {
          continue;
        }
        fromTime = row.fromTime || defFromTime;

        postData["checkPara[" + vi + "].idxId"] = (idxId || null);
        postData["checkPara[" + vi + "].orgId"] = row.organizationId;
        postData["checkPara[" + vi + "].orgPartId"] = row.orgPartId;
        postData["checkPara[" + vi + "].value"] = val;
        postData["checkPara[" + vi + "].fromTime"] = fromTime;
        postData["checkPara[" + vi + "].periodId"] = (row.calcPeriod || null);

        ++vi;
      }
      //
      var ajaxOpts = {
        url: global.platform.indexValidatorAction
        , type: "POST"
        , data: postData
        , dataType: "json"
        , success: function (res) {
          //console.log(res);
          var msg = !!res ? res["message"] : null;
          if (!!msg) {
            vm.$alert(msg);
          }

          //
          var validateResult = {}, validateNumErrors = 0, validateNumWarnings = 0;
          //具体结果
          var dat = !!res && res["data"] || null;
          if (!!dat && dat.length > 0) {
            var lv, warningMsg = [], infoMsg = [];
            for (i = 0; i < dat.length; i++) {

              row = dat[i];
              lv = row["validLevel"].toLowerCase();
              msg = row["msg"];

              lv = "warn" == lv ? "warning" : lv;

              validateResult[row["idxId"]] = {"message": msg, "type": lv};
              if ("error" == lv) {
                ++validateNumErrors;
              } else if ("warning" == lv) {
                ++validateNumWarnings;
              }
            }
          }
          vm.validator.result = validateResult;
          vm.validator.numErrors = validateNumErrors;
          vm.validator.numWarnings = validateNumWarnings;
          //
          if ("function" == $.type(successCallback)) {
            successCallback();
          }
        }

        , error: function (x, s, e) {
          try {
            var res = eval("(" + x.responseText + ")");
            var msg = res["message"];
            if (!!msg) {
              vm.$alert(msg);
            }
          } catch (err) {
            console.error(err);
          }
        }
      };

      $.ajax(ajaxOpts);
      //
    },

    selectJobType: function (params) {
      self = this;
      // params = params.split(",");
      // let value = params[0]; // 获取到的value
      // let label = params[1]; // 获取到的label
      //var lastjobPeriod = self.form.value_jobPeriod;
      self.form.value_jobPeriod =
        formConfig.value_jobType[self.form.value_jobType].period;
      // if ("4" == self.form.value_jobPeriod && "4" != lastjobPeriod) {
      // }
    }
  }
};
