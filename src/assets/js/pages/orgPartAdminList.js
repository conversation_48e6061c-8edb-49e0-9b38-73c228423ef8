import global from "@/config/global-config";

import M<PERSON><PERSON><PERSON> from "@/utils/MQUtil";

import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";
import formConfig from "@/config/uploadLog-config";
import { registerMap } from "echarts";

export default {
  name: "page_orgPartAdminList",
  
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      dataTableHeight: "100%",
      loading: false,

      tableDataAction: global.cmis.domain + "/carePlatform/orgPartAdmin/findList",

      loadingMask: null,

      form: {
        visible: PageUtil.searchFormVisibility(),

        userName: null,
        mobile: null,
        phone: null,
        email: null
      },

      editForm: {
        visible: false,

        editable: false,

        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,

        loginCode: null,
        orgId: [],
        orgPartId: [],
        userName: null,
        mobile: null,
        phone: null,
        email: null,
        oldLoginCode: null,
        userCode: null,
        employee: null
      }

    };
  },

  created: function() {
    this.layoutPage();

    this.getTableData();

    //监听是否有病区更新
    MQUtil.register("orgPart", "orgPartAdmin", "lastUpdated");
  },

  /**
   * 激活组件
   */
  activated: function() {
    var message = MQUtil.getMessage("orgPart", "orgPartAdmin", "lastUpdated");
    if(message) {
      this.editForm.combo_orgPartId = formConfig.combo_orgPartByOrg;
    }
  },

  methods: {

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },
    getTableData: function() {
      var vm = this, frm = vm.form;
      //清空表格
      vm.tableData = [];
      vm.total = 0;

      vm.loading = true;
      //请求数据
      var ajaxOpts = {

        type: "post",
        url: vm.tableDataAction,
        data: {
          pageNo: vm.currentPage,
          pageSize: vm.pageSize,

          "userName": frm.userName,
          "mobile": frm.mobile,
          "phone": frm.phone,
          "email": frm.email
        },
        dataType: "json",
        success: function(res) {
          vm.loading = false;

          vm.tableData = res.list;
          vm.total = res.count;
        },
        error: function() {
          vm.loading = false;

          vm.$alert("加载失败");
        }
      };

      AjaxUtil.send(ajaxOpts);
    },
    onSearch: function() {
      this.getTableData();
    },

    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var vm = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    },

    edit: function(item, opt) {
      var vm = this, editForm = vm.editForm;

      item = item || {};

      editForm.visible = vm.editForm.editable = true;

      editForm.editable = 'view' != opt;
      //
      var path, emp, orgPartId = null;
      if(!!item && !!(emp = item.employee) && !!emp.office && !!emp.office.officeCode) {
        path = vm.findPath("orgPart", emp.office.officeCode);
        orgPartId = path? path.split(",") : null;
      }

      editForm.orgPartId = orgPartId || [];
      editForm.loginCode = item.loginCode || null;
      editForm.userName = item.userName || null;
      editForm.mobile = item.mobile || null;
      editForm.phone = item.phone || null;
      editForm.email = item.email || null;
      editForm.oldLoginCode = item.loginCode || null;
      editForm.userCode = item.userCode || null;
      editForm.employee = !!emp? {empCode: emp.empCode} : null;
      //所属单位/医院, 新建默认同当前用户一单位
      //根据用户机构，获取机构对应的科室
      if(!editForm.userCode) {
        editForm.orgId = formConfig["value_user"] && formConfig["value_user"]["org_tree_ids"] || [];
        editForm.combo_orgPartId = formConfig.combo_orgPartByOrg;
        
      } else {
        path = !!emp && !!emp.company && !!emp.company.companyCode? vm.findPath("organization", emp.company.companyCode) : null;
        editForm.orgId = path && path.length? path.split(",") : [];
      }
    },

    del: function(item) {

    },

    confirmToDo: function(cmd) {
			var vm = this, m = "是否确定?";

			vm.$confirm(m, '确认').then(() => {
				switch(cmd) {
					case 'save':
						vm.save();
						break;
					case 'delete':
						vm.del(arguments[1]);
						break;
					}
				}).catch(() => {});
    },

    save: function() {
      var vm = this;

      var frm = vm.editForm;
      var orgId = (frm.orgId && frm.orgId[frm.orgId.length - 1] || null)
        , orgPartId = (frm.orgPartId && frm.orgPartId[frm.orgPartId.length - 1] || null)
        , userName = frm.userName
        , mobile = frm.mobile
        , phone = frm.phone
        , email = frm.email;

      var errM = [];
      if(!orgId || !orgId.length) {
        errM.push("请选择目录;");
      }
      if(!orgPartId || !orgPartId.length) {
        errM.push("请选择所属单位;");
      }
      if(!userName) {
        errM.push("请填写姓名;");
      }
      if(!mobile) {
        errM.push("请填写手机号;");
      }
      if(!phone) {
        errM.push("请填写病区固定电话;");
      }
      if(!email) {
        errM.push("请填写邮箱;");
      }
      if(errM.length) {
        vm.$alert(errM.join("<br/>"), {dangerouslyUseHTMLString: true});
        return;
      }

      //请求数据
      var ajaxOpts = {

        type: "post",
        url: global.cmis.domain + "/carePlatform/orgPartAdmin/save",
        data: {

          "employee.office.id": orgPartId,
          "employee.company.id": orgId,
          "loginCode": frm.loginCode,
          "userName": userName,
          "mobile": mobile,
          "phone": phone,
          "email": email,
          "oldLoginCode": frm.oldLoginCode,
          "userCode": frm.userCode,
          "employee.empCode": (!!frm.employee? frm.employee.empCode : null)
        },
        dataType: "json",
        success: function(res) {
          var M = res? res["message"] : null;
          if(M) {
            vm.$alert(M);
          }

          if(res && "true" == res["result"]) {
            vm.editForm.visible = false;
            
            vm.getTableData();

            //vm.edit(res["data"]);
          }
        },
        error: function() {

          vm.$alert("保存失败.");
        }
      };

      AjaxUtil.send(ajaxOpts);
    },
    
    del: function(item) {
      var vm = this;

      AjaxUtil.send({
        url: global.cmis.domain + "/carePlatform/orgPartAdmin/del?userCode=" + item.userCode,
        async: false,
        dataType: "json",
        success: function(res) {
          var M = res? res["message"] : null;

          if(res && "true" == res["result"]) {
            M += "登录用户名为:" + res["loginCode"];
            if(!frm.loginCode) {
              M += ",密码为:123456, 请登录后进行修改."
            }

            vm.getTableData();
          }

          if(M) {
            vm.$alert(M);
          }
        },
        error: function(x, errM, err) {
          console.error(err);
        }
      });
    },
    /**
     * 基础表机构/单位表数据字典
     * @param {*} type 
     * @param {*} value 
     */
    findPath: function(type, value) {
      var path = null;

      AjaxUtil.send({
        url: global.cmis.domain + "/carePlatform/orgPart/findPath?dictType=" + type + "&dictCode=" + value,
        async: false,
        dataType: "json",
        success: function(res) {
          path = res? res.data : null;
        },
        error: function(x, errM, err) {
          console.error(err);
        }
      });

      return path;
    }
  }
};
