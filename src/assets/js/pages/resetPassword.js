import globalVariables from '@/config/global-config'
import DesUtil from '@/utils/DesUtil'
import {validatePasswd} from "@/utils/ValidatorUtil";

//两次短信验证码请求间隔, 秒钟
const requireSmsCodeInterval = 1 * 60;

export default {
  data(){
    //两次密码是否一致
    var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.form.newPassword) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      
    return {
      title: globalVariables.productName,
      logining: false,
      form: {
          mobile: null,
          smsCode: null,
          newPassword: null,
          confirmNewPassword: null,
          verificationNo: null,
      },
      formRules: {
          mobile: [{required: true, message: '请输入登录账号或手机', trigger: 'blur'}],
          smsCode: [{required: true, message: '请输入短信验证码', trigger: 'blur'}],
          newPassword: [{required: true, message: '请输入新密码',trigger:'blur'},
          {validator:validatePasswd,trigger:'blur'}],
          confirmNewPassword: [{required: true, message: '请输入确认新密码', trigger: 'blur'},
          {validator: validatePass2,trigger:'blur'}],
      },

      securityKey: globalVariables.securityKey,

      tipper: null,

      lastTimeRequireSmsCode: 0
    }
  },
  methods: {

    /**
     * 确认操作
     */
    confirmTodo: function() {
      var vm = this;
      vm.$confirm('是否确定?', '确认').then(vm.handleSubmit).catch($.noop);
    },

    /**
     * 重置
     */
    handleSubmit(){
      var vm = this;

      vm.$refs.mainForm.validate((valid) => {
        if(!valid){ 
          return false;
        }

        var fm = vm.form;
        var secKey = vm.securityKey;
        //重置要求的参数
        var postData = {
          mobile: DesUtil.encode(fm.mobile, secKey),
          smsCode: fm.smsCode,
          newPassword: DesUtil.encode(fm.newPassword, secKey),
          confirmNewPassword: DesUtil.encode(fm.confirmNewPassword, secKey),
          verificationNo: fm.verificationNo
        };

        var ajaxOpts = {
          url: globalVariables.cmis.domain + '/sso/auth/resetPassword'
          , type: 'post'
          , dataType: 'json'
          , data: postData
          , success: function(res) {
            vm.logining = false;
            if(!res) {
              return;
            }
            //失败
            var msg = res["message"];
            if(!!msg && "true" != res["result"]) {
              vm.$alert(msg);
              return;
            }
        
            vm.residentTipper();
            //成功
            vm.$confirm('操作成功.', '信息', {
              confirmButtonText: '登录平台',
              cancelButtonText: '留在这里',
            }).then(() => {
              vm.goto({name: "登录"});
            }).catch($.noop);
          }
          , error: this.handleError
        };

        vm.logining = true;
        $.ajax(ajaxOpts);
      
        return true;
      })
    },

    /**
     * 获取验证码
     */
    requireSmsCode: function() {
      var timeMillis = new Date().getTime() / 1000;
      if(!!this.lastTimeRequireSmsCode && (timeMillis - this.lastTimeRequireSmsCode) <= requireSmsCodeInterval) {
        this.residentTipper("获取短信验证码过于频繁.", "warning");
        return;
      }

        var vm = this, fm = vm.form;
        var secKey = vm.securityKey;
        //重置要求的参数
        var postData = {
          mobile: DesUtil.encode(fm.mobile, secKey),
        };

        var ajaxOpts = {
          url: globalVariables.cmis.domain + '/sso/auth/makeSmsCode'
          , type: 'post'
          , dataType: 'json'
          , data: postData
          , success: function(res) {
            vm.logining = false;
            if(!res) {
              return;
            }
            //失败
            var msg = res["message"];
            if(!!msg && "true" != res["result"]) {
              vm.$alert(msg);
              return;
            }
            vm.lastTimeRequireSmsCode = timeMillis;
            //验证码序号
            var verificationNo = res["data"];
            fm.verificationNo = verificationNo;
            //
            msg = "验证码已发送, 请输入序号为" + verificationNo + "的验证码.";
            vm.residentTipper(msg);
          }
          , error: this.handleError
        };

        vm.residentTipper();

        vm.logining = true;
        $.ajax(ajaxOpts);
    },

    /**
     * 处理错误
     */
    handleError: function() {
      this.loading = false;
      this.$alert("操作失败.");
    },

    /**
     * 页面跳转
     */
    goto: function(route) {
      this.$router.push(route);
    },

    /**
     * 顶部提示
     */
    residentTipper: function(mess, type) {
      var vm = this;
      //
      if(null != vm.tipper) {
        vm.tipper.close();
      }
      if(!mess) {
        return;
      }

      vm.tipper = vm.$message({message: mess, type: (type || "success"), duration: 0, showClose: true});
    }
  },

  /**
   * 离开当前页
   */
  beforeDestroy: function() {
    
    this.residentTipper();
  }
};
