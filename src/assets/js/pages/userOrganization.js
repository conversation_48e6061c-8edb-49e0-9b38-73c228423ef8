import global from '@/config/global-config'

import userInfo from "@/components/pages/userInfo";
	
import AjaxUtil from '@/utils/AjaxUtil'

import formConfig from "@/config/uploadLog-config";

export default {
	name: "page_userOrganization",

	props: {
    "editMode": {"default": "standalone"}
  },
	
	data () {
		return {

			loading: false, 

			form: {
				enableEdit: false,
				underEdit: false,

				readAction: global.cmis.domain + "/carePlatform/dataChange/get?z",
				saveAction: global.cmis.domain + "/carePlatform/dataChange/save",
				submitAction: global.cmis.domain + "/carePlatform/dataChange/submit",
				uploadAction: global.cmis.domain + "/carePlatform/dataChange/uploadUndertaking",
				downloadAction: global.cmis.domain + "/carePlatform/dataChange/downloadUndertaking?z",
				//可编辑字段
				fields: [
					{"desc": "机构名称", "tableName": "D_ORGANIZATION", "columnName": "DICT_LABEL"},
					{"desc": "机构代码", "tableName": "D_ORGANIZATION", "columnName": "DICT_VALUE"},
					{"desc": "机构定级", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S1"},
					{"desc": "机构定等", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S2"},
					{"desc": "机构类型", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S3"},
					{"desc": "所有制性质", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S4"},
					{"desc": "行政区划", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S5"},
					//{"desc": "该单位上报的科室", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S6"},
					{"desc": "临床教学基地类型", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_I1"},
					{"desc": "编制床位数", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_I2"},
					{"desc": "注册护士数", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_I3"},
					{"desc": "隶属类型", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_I4"},
					{"desc": "是否上报国家", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_F1"},
					{"desc": "审核通过时间", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_D1"},
					{"desc": "承诺书", "tableName": "D_ORGANIZATION", "columnName": "EXTEND_S7"},
				],
				//修改的数据
				items: [],

				//任务信息
				reportResult: null,
				//注册加入传参机构id, 
				orgId: null,
				//注册加入传参用户代码
				userCode: null,
				//审核流程
				checkFlow: null,
				//下拉选项
				combo_orgId: formConfig.combo_orgPartIdHierarchical,//
				combo_orgGrade: formConfig.combo_orgGrade,
				combo_orgClass: formConfig.combo_orgClass,
				combo_medinstCategory: formConfig.combo_medinstCategory,
				combo_yesno: formConfig.combo_yesno,
				combo_orgProp: formConfig.combo_orgProp,
				//combo_area: formConfig.combo_areaHierarchical,
				combo_orgAttachType: formConfig.combo_orgAttachType,
				combo_clinicaledutype: formConfig.combo_clinicaledutype,

				undertakingAcceptedFileTypes: ["pdf", "doc", "docx", "jpg", "jpeg"],

				combo_area_visibility: false
			},

			approveForm: {
				visible: false,
				enableApprove: false,
				underApprove: false,

				checkState: null,
				checkFeedback: null,

				combo_checkState: [{value: "2", label: "通过"}, {value: "4", label: "不通过"}],

				user_avatar: global.cmis.domain + "/static/images/user1.jpg",
				checkData: []
			},

			nestCompanyManager: true
		}
	},
	components: {
		companyManager: userInfo
	},
	created: function(){
		var params = this.$route.params, pval;
		if((pval = params["reportResult.id"])) {
			this.form["reportResult"] = {id: pval};
		}
		//注册加入传参
		if(this.isRegisterMode) {
			if(!!(pval = params["organization.id"])) {
				this.form["orgId"] = pval;
			}
			if(!!(pval = params["user.userCode"])) {
				this.form["userCode"] = pval;
			}
		}

		this.readData();
	},
  methods: {

		/**
		 * 读取信息
		 */
		readData: function() {
			var self = this;
			//默认空白数据
			var fields = self.form.fields, formData = [];
			//初始化, 后面这些字段值改变才出发数据绑定
			for(var i = 0, item; i < fields.length; i ++) {
				formData.push(item = {});
				$.extend(item, fields[i], {"valueBefore": null, "valueAfter": null});
			}
			self.form.items = formData;

			var page = self.form.readAction;
			if(self.form["reportResult"] && self.form["reportResult"]["id"]) {
				page += "&reportResult.id=" + self.form["reportResult"]["id"];
			}
			if(self.form["orgId"]) {
				page += "&organization.id=" + self.form["orgId"];
			}
			//self.form.reportResult = null;
			//读取
			this.loading = true;
			var ajaxOpts = {
				"url": page
				, "dataType": "json"
				, "success": function(res) {
					self.loading = false;
					//console.log(res);
					//提示信息
					var errM = res["message"];
					if(errM) {
						//用户信息不完善
						if(403 == res["errorCode"]) {

							self.$confirm(errM, "注意", {
								confirmButtonText: '编辑个人信息',
								cancelButtonText: '关闭',
								type: 'warning'
							}).then(() => {
								self.$router.push({"name": "个人中心"});
							}).catch(() => {});

						} else {
							self.$alert(errM);
						}
					}
					if("false" == res["result"]) {
						return;
					}
					//
					var dat = res["data"], resData = null;
					if(dat) {
						resData = dat["items"];
						self.form.reportResult = dat["reportResult"] || null;
						self.form.checkFlow = dat["checkFlow"] || null;
					}
					//返回数据覆盖空白
					var i, fr, j = 0, rr,tab, col, k;
					for(i = 0; i < formData.length; i ++) {
						fr = formData[i];
						tab = fr["tableName"];
						col = fr["columnName"];
						
						for(j = 0; resData && j < resData.length; j ++) {
							rr = resData[j];
							//表字段值覆盖
							if(tab == rr["tableName"] && col == rr["columnName"]) {
								//地区, 上报科室, 分割成数组
								if(tab == "D_ORGANIZATION" && (col == "EXTEND_S5" || col == "EXTEND_S6")) {
									var areaId = rr["valueBefore"];
									rr["valueBefore"] = areaId? areaId.split(",") : null;

									areaId = rr["valueAfter"];
									rr["valueAfter"] = areaId? areaId.split(",") : null;
								}
								//填空白
								$.extend(fr, rr);
								//加载修改前行政区划
								if(col == "EXTEND_S5") {
									self.form.combo_area_visibility = true;
								}

								break;
							}
						}
					}
					//
					var companyManagerData = res["companyManager"], compMana;
					if(!self.isRegisterMode && companyManagerData && companyManagerData.length && (compMana =self.$refs["compMana"])) {
						compMana.updateData(companyManagerData[0]);
					}
					//更新表单
					self.form.items = formData;
					//
					self.isEnableEdit();
					//用户信息不完整不允许修改
					self.form.enableEdit = !!(self.form.enableEdit && !res["errorCode"]);
					//
					self.isUnderApprove();
					//注册加入, 允许编辑
					if(self.isRegisterMode) {
						self.form.enableEdit = self.form.underEdit = true;
						self.prepareEdit();
					}
				}
				, error: function() {self.loading = false;}
			};

			AjaxUtil.send(ajaxOpts, true);
		},
  	/**
   	 * 确认操作
   	 */
		confirmToDo: function(cmd, opts) {
			var self = this, cf = self.form.checkFlow;
			var m = "是否确定?";
			//最后审核通过提示
			if('approve' == cmd && cf && !!cf.endNodeId && cf.endNodeId == cf.nextNodeId && "2" == self.approveForm.checkState) {
				m = "审核通过将对申请机构的信息进行修改, 是否继续?";

			} else if("submit" == cmd) {
				m = "请输入修改理由, 有助于审核顺利通过.";
				self.$prompt(m, "提示").then(({ value }) => {
					if(!value) {
						self.$alert(m);
						return;
					}
					opts = opts || {};
					opts["checkFeedback"] = value;

          self.saveData(cmd, opts);
        }).catch(() => {});

        return;
			}

			self.$confirm(m, '确认').then(() => {
				switch(cmd) {
					case 'save':
					//case 'submit':
						self.saveData(cmd, opts);
						break;
					case 'approve':
						self.approve();
						break;
					}
				}).catch(() => {});
		},

		/**
		 * 开启编辑
		 */
		prepareEdit: function() {
			var frm = this.form;

			frm.underEdit = true;

			var formData = frm.items;
			for(var i = 0, row; i < formData.length; i ++) {
				row = formData[i]
				if(("D_ORGANIZATION" == row["tableName"] && "EXTEND_D1" == row["columnName"])||
				("D_ORGANIZATION" == row["tableName"] && "DICT_LABEL" == row["columnName"])) {
					continue;
				}
				row["valueAfter"] = row["valueBefore"];
			}
		},
		/**
		 * 保存数据
		 */
		saveData: function(cmd, opts) {
			var self = this;
			//错误
			var errM = [];
			//表单数据, 提交的数据
			var frm = self.form, formData = frm.items, postData = {"cmd": cmd};
			//任务信息
			postData["reportResult.id"] = frm["reportResult"] && frm["reportResult"]["id"] || null;
			for(var i = 0, row, tab, col, val, k; i < formData.length; i ++) {
				row = formData[i];
				tab = row["tableName"];
				col = row["columnName"];
				//
				for(k in row) {
					//地区, 只要叶子
					val = row[k];
					if(val && ("valueBefore" == k || "valueAfter" == k)
						&& "D_ORGANIZATION" == tab && "EXTEND_S5" == col) {
							val = val[val.length - 1];
					}
					//医院信息目前不让修改，但需要入库，方便后续查看机构信息
					//后续可以开放编辑
					if("valueAfter"==k &&"D_ORGANIZATION" == tab && "DICT_LABEL" == col)
					{
						val = row["valueBefore"];
					}

					postData["items[" + i + "]." + k] = val;
					//
					if("valueAfter" == k && 
					("D_ORGANIZATION" != tab ||
					"EXTEND_D1" != col ) && 
					!val) {
						errM.push(row["desc"]);
					}
				}
			}
			if(errM.length) {
				self.$alert("请补充如下信息: <br/>" + errM.join(", "), {dangerouslyUseHTMLString: true});
				return;
			}
			//注册传入, 注册人信息
			if(!!(self.form["userCode"])) {
				postData["user.userCode"] = self.form["userCode"];
				postData["organization.id"] = self.form["orgId"];
			}
			//修改理由
			postData["reportResult.lastCheckFeedback"] = opts? opts["checkFeedback"] : null;
			
			var ajaxOpts = {
				"url": self.form.saveAction
				, "type": "post"
				, "dataType": "json"
				, "data": postData
				, "success": function(res) {
					self.loading = false;
					//console.log(res);
           //其他回调
          var callback = opts? opts["successHandler"] : null;
          if("function" == $.type(callback)) {
            callback(res);
          } else {
						//提示信息
						if(res["message"]) {
							self.$alert(res["message"]);
						}
						//成功
						if("true" == res["result"]) {
							self.readData();
						}
					}
				}
				, error: function() {self.loading = false;}
			};
			
			self.loading = true;

			AjaxUtil.send(ajaxOpts, true);
		},

		/**
		 * 下载承诺书
		 */
		downloadUndertaking: function() {
			var self = this, frm = self.form;
			var row = self.findUndertaking(), undertaking = self.efficientUndertaking(row);

			if(!undertaking) {
				self.$alert("请上传承诺书.");
				return;
			}

			var page = self.form.downloadAction;
			//指定申请记录id
			if(row && row.id) {
				page += "&id=" + row.id;
			} else {
				//指定承诺书文件名称
				page += "&fileName=" + encodeURIComponent(undertaking);//escape(escape(undertaking));

			}

			top.open(page);
		},
		/**
		 * 完成上传承诺书, 更新存放承诺书的字段
		 */
		onUploadUndertaking: function(response, file, fileList) {
			var row = this.findUndertaking();
			if(row) {
				row["valueAfter"] = response["data"];
			}
		}, 
		/**
		 * 检查文件类型
		 */
		beforeUploadUndertaking: function(file) {
			var acceptedTypes = this.form.undertakingAcceptedFileTypes;
			var fileName = file.name, pos = fileName.lastIndexOf(".");
			if(-1 == pos) {
				return false;
			}
			var fileExt = fileName.substring(pos + 1).toLowerCase();
			if(-1 == $.inArray(fileExt, acceptedTypes)) {
				this.$alert("不支持的文件类型, 请重新上传.");
				return false;
			}

			return true;
		},

		/**
		 * 承诺书存放的字段
		 */
		findUndertaking: function() {
			var formData = this.form.items;
			if(!formData || !formData.length) {
				return null;
			}
			for(var i = 0, row; i < formData.length; i ++) {
				row = formData[i];
				if("D_ORGANIZATION" == row["tableName"] && "EXTEND_S7" == row["columnName"]) {
					return row;
				}
			}
			return null;
		},
		
		/**
		 * 打开审核对话框
		 */
		prepareApprove: function() {
			this.approveForm.visible = true;

			this.findApprove();
		},

		/**
		 * 读取审核记录
		 */
		findApprove: function() {
			var self = this, frm = self.form;
			if(frm["reportResult"] && !frm["reportResult"]["id"]) {
				self.$alert("没有任务.");
				return;
			}
	  
			var ajaxOpts = {
			  url: global.cmis.domain + "/carePlatform/check/findList",
			  dataType: "json",
			  data: { "reportResult.id": frm["reportResult"]["id"] },
			  success: function(res) {
				//console.log(res);
				//成功, 刷新列表
				if ("true" == res["result"]) {
				  self.approveForm.checkData = res["data"] || [];
				  //滚动到最新审批
				  setTimeout(function() {
					var checkDataPane = document.getElementById("checkDataPane"); //$("#checkDataPane").get(0);
					if(checkDataPane) {
					  checkDataPane.scrollTop = checkDataPane.scrollHeight;
					}
				  }, 200);
				}
			  }
			};
			AjaxUtil.send(ajaxOpts);
		},
	  
		changeState:function(label) {
			var self=this;
			if(label=='2')
			{
			   self.approveForm.checkFeedback='通过.';
			}
			else
			{
			   self.approveForm.checkFeedback='';
			}
		  },
		approve: function() {
			var self = this;
			var checkState = self.approveForm.checkState;
			var checkFeedback = self.approveForm.checkFeedback;
			if (!checkState || !checkFeedback) {
			  self.$alert("请选择'审核状态'并填写'审核意见'.");
			  return;
			}
			//ajax
			var ajaxOpts, formData = {}, abortApprove = false;
			//
			var frm = self.form;
			formData["reportResult.id"] = self.form["reportResult"]["id"];
			formData["checkFeedback"] = checkFeedback;
			//最后的审核
			if(frm.checkFlow.nextNodeId == frm.checkFlow.endNodeId) {
				var url;
				//审核通过
				if("2" == checkState) {
					url = global.cmis.domain + "/carePlatform/dataChange/completeChange";
				} else if("4" == checkState) {
					url = global.cmis.domain + "/carePlatform/dataChange/rejectChange";
				}
				
				//审核不通过
				ajaxOpts = {
					url: url,
					type: "post",
					dataType: "json",
					data: formData,
					async: false,
					success: function(res) {
						if(!res || "true" != res["result"]) {
							abortApprove = true;
						}
					},
					error: function() {
						abortApprove = true;
					}
				};
				AjaxUtil.send(ajaxOpts);
			}
			if(abortApprove) {
				return;
			}
			//
			formData["checkState"] = checkState;
	  
			ajaxOpts = {
			  url: global.cmis.domain + "/carePlatform/check/approve",
			  type: "post",
			  dataType: "json",
			  data: formData,
			  success: function(res) {
					//console.log(res);
					//提示信息
					var msg = res["message"];
					if (msg) {
					  //self.$alert(msg);
					  self.$alert(msg, "提交结果", {
						callback: function() {
						  if ("true" == res["result"]) {
							self.approveForm.visible = false;
						  }
						}
					  });
					}
					//成功, 刷新表格
					if ("true" == res["result"]) {
					  //重置表单
					  self.approveForm.checkState = "";
					  self.approveForm.checkFeedback = "";
					  //self.approveForm.visible = false;
						
					  self.findApprove();

					  self.readData();
					}
			  }
			};
			AjaxUtil.send(ajaxOpts);
		},
		/**
		 * 设置能否编辑
		 */
		isEnableEdit: function() {
			var frm = this.form;
			//没有保存或者没有提交的可以修改
			//this.form.enableEdit = !frm.reportResult || !frm.reportResult.id || !frm.checkFlow || !frm.checkFlow.lastNodeId;
			this.form.enableEdit = !!(!frm.reportResult || !frm.reportResult.id || !frm.checkFlow 
				|| !!frm.checkFlow && !frm.checkFlow.lastNodeId && frm.checkFlow.nextNodeId && frm.checkFlow.nextCheckUsersId);
			//保存了但没有流程
			this.form.underEdit = !!(this.form.enableEdit && !!frm.reportResult && !!frm.reportResult.id);
		},
		/**
		 * 设置是否处于审核状态
		 */
		isUnderApprove: function() {
			var frm = this.form;
			//可以看到审核信息
			this.approveForm.enableApprove = !!(frm.checkFlow && !!frm.checkFlow.lastNodeId);
			//可以审核
			this.approveForm.underApprove = !!(frm.checkFlow && frm.checkFlow.lastNodeId 
				&& frm.checkFlow.nextNodeId && frm.checkFlow.nextCheckUsersId);
		},

		flowStepStatus: function(step, flow) {
			return !!flow && step == flow.lastNodeId && '4' == flow.lastCheckState? 'error' : '';
		},

		/**
		 * 显示的承诺书
		 * @param item 修改的D_ORGANIZATION.EXTEND_S7数据行
		 */
		efficientUndertaking: function(item) {
			return item["valueAfter"] || item["valueBefore"];
		},

		areaCascaderProps: function(defVal) {
			var frm = this.form;

			return (function(frm, defVal) {
				return {
					checkStrictly: true,
					lazy: true,
					lazyLoad (node, resolve) {
	        	//console.log(node);
	        	//console.log(defVal);

	        	var nodeData = node["data"];
	        	var ajaxParams = {dictType: "area"};
	        	if(node.root && !!defVal && !!defVal.length) {
	        		//初始, 带初始值
							ajaxParams["paths"] = "array" == $.type(defVal)? defVal.join(",") : defVal;
	        	} else {
							ajaxParams["treeLevel"] = node["level"];
	          	if(node.root) {
								ajaxParams["parent.dictCode"] = "0";
	          	} else {
								ajaxParams["parent.id"] = nodeData["value"];
	          	}
	        	}
	        	var ajaxOpts = {
	        		url: global.cmis.domain + "/carePlatform/index/readFormCombo"
	        		, async: true
	        		, type: "post"
	        		, data: ajaxParams
	        		, dataType: "json"
	        		, success: function(res) {
	        			if("true" == res["result"]) {
	        				var dat = formConfig.makeOptions(res["area"]);
	        				resolve(dat);
	        			}
	        		}
	        	};

	        	AjaxUtil.send(ajaxOpts, true);
	        }
	      };
			})(frm, defVal)
    }
	},

	computed: {
		/**
		 * 是否通过平台注册加入
		 */
		isRegisterMode: function() {
			return "register" == this.editMode;
		}
	}
}
