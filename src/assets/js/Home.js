import Header from "@/components/Header";
import Sider from "@/components/Sider";
import Footer from "@/components/Footer";

import LoginHelper from "@/utils/LoginHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  name: "app",
  data() {
    return {
      mainBodyContainerHeight: "auto"
    };
  },
  components: {
    vheader: Header,
    vsider: Sider,
    vfooter: Footer
  },

  methods: {
    //tab标签点击时，切换相应的路由
    tabClick(tab) {
      this.$router.push({ path: this.activeIndex });
    },
    //移除tab标签
    tabRemove(targetName) {
      //console.log("tabRemove",targetName);
      //首页不删
      if (targetName == "/" || targetName == "/main") {
        return;
      }
      this.$store.commit("delete_tabs", targetName);
      if (this.activeIndex === targetName) {
        // 设置当前激活的路由
        var openTabs = this.$store.state.openTab;
        if (openTabs && openTabs.length >= 1) {
          this.$store.commit(
            "set_active_index",
            openTabs[openTabs.length - 1].path
          );
          this.$router.push({ path: this.activeIndex });
        } else {
          this.$router.push({ path: "/main" });
        }
      }
    },

    resize: function() {
      var self = this;
      //页面高
      setTimeout(() => {
        
        var height = document.documentElement.clientHeight;
        //减头部
        height -= $("#mainHeader").outerHeight();
        //减Tab标签
        height -= $("#mainTabContainer").outerHeight();
        //减底部
        height -= $("#mainFooter").outerHeight();
        
        self.mainBodyContainerHeight = height + "px";;
        
      }, 666);
    },

    initTabs: function() {
      var self = this;
      //首页
      var mainRouter = { path: "/main", name: "首页" };
      //当前路由
      var thsRoute = self.$route;
      //初始打开的tab
      //必须首页
      this.$store.commit("add_tabs", mainRouter);
      //
      var activePath = mainRouter.path;
      if (
        thsRoute.path !== "/" &&
        thsRoute.path !== "/home" &&
        thsRoute.path !== mainRouter.path
      ) {
        this.$store.commit("add_tabs", {
          path: thsRoute.path,
          name: thsRoute.name
        });
        activePath = thsRoute.path;
      }
      this.$store.commit("set_active_index", activePath);
    },

    setupPage: function() {
    
      this.resize();

      this.initTabs();
    }
  },

  activated: function() {

    this.setupPage();
  },

  created: function() {
    //要求权限
    if(!LoginHelper.logincheck()) {
      return;
    }

    formConfig.init();
  },

  mounted: function() {
    //console.log("home mounted.");

    this.setupPage();
  
    window.onresize = () => {
        return (() => {
          //console.log("resizing");
          this.resize();
      })()
    }

  },
  computed: {
    openTab() {
      var openTabs = this.$store.state.openTab;
      //tab打开home
      openTabs = openTabs.slice(0);
      for (var i = openTabs.length - 1; i >= 0; i--) {
        if ("home" == openTabs[i].name) {
          openTabs.splice(i, 1);
          break;
        }
      }

      return openTabs;
    },
    activeIndex: {
      get() {
        return this.$store.state.activeIndex;
      },
      set(val) {
        this.$store.commit("set_active_index", val);
      }
    },

    cacheComponents() {
      var compNames = [], openTabs = this.openTab;
      
      for (var i = openTabs.length - 1; i >= 0; i--) {
        //组件定义的 name, 不是路由 name
        compNames.push("page_" + openTabs[i].path.substring(1));
      }
      //console.log(compNames);
      return compNames;
    }
  },
  watch: {
    $route(to, from) {
      var vm = this;
      //keep-alive的include要生效, 路由要在Tab之前
      setTimeout(() => {
        vm.$store.commit("add_tabs", to);
        vm.$store.commit("set_active_index", to.path);
      }, 0);
    }
  }
};
