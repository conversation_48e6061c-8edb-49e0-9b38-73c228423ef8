import globalVariables from '@/config/global-config'
import LoginHelper from '@/utils/LoginHelper'
import DesUtil from '@/utils/DesUtil'
import StoreUtil from '@/utils/StoreUtil'
import StringUtil from '@/utils/StringUtil'

const verifyCodeId = StringUtil.randomString();
const kaptchaImage = globalVariables.sso.domain + "/kaptcha?verifyCodeId=" + verifyCodeId;
const maxLoginFailTimes = 10;

export default {
  data(){
    return {
      title: globalVariables.productName,
      logining: false,
      loginForm: {
          username: '',
          password: '',
          rememberme: false,
          cacheKey: "loginForm"
      },
      loginRule: {
          username: [{required: true, message: '请输入用户名', trigger: 'blur'}],
          password: [{required: true, message: '请输入密码', trigger: 'blur'}],
          verifyCode: [{required: true, message: '请输入验证码', trigger: 'blur'}]
     },

      securityKey: globalVariables.securityKey,

      verifyCodeImage: kaptchaImage,
      loginFailTimes: 0
    }
  },
  methods: {
    handleSubmit(event){
      var self = this;

      if(self.loginFailTimes >= maxLoginFailTimes) {
        self.$alert("登录失败次数已达" + maxLoginFailTimes + "次, 请尝试重置密码或联系管理员取回账号.");
        return;
      }

      self.$refs.loginForm.validate((valid) => {
        if(!valid){
          return false;
        }

        self.logining = true;
        var fm = self.loginForm;
        var secKey = self.securityKey;

        var username = DesUtil.encode(fm.username, secKey);
        var password = DesUtil.encode(fm.password, secKey);
        var verifyCode = fm.verifyCode;

        var prin = {username: username, password: password, verifyCodeId: verifyCodeId, verifyCode: verifyCode};
        LoginHelper.auth(prin, self.loginSuccess, function(errC, errM) {
          self.logining = false;
          //
          self.refreshVerifyCode();
          //
          if(40100 == errC) {
            self.loginFailTimes += 1;
            errM = !!errM? (errM + "(错误次数=" + self.loginFailTimes + ")") : errM;
          }

          self.$alert(errM || "登录失败, 请稍后重试.");
        });

        return true;

      })
    },
    loginSuccess: function() {
      this.logining = false;
      //
      self.loginFailTimes = 0;
      //
      this.rememberme();
      //跳到首页
      this.$router.push({'name': '首页'});
      //刷新，触发home的mounted
      location.reload();
    },

    /**
     *
     */
    rememberme: function() {
      var vm = this, fm = vm.loginForm;
      if(fm.rememberme) {
        var secKey = vm.securityKey;

        var username = DesUtil.encode(fm.username, secKey);
        var password = DesUtil.encode(fm.password, secKey);

        StoreUtil.save(fm.cacheKey, {username: username, password: password, rememberme: true});
      } else {
        StoreUtil.clear(fm.cacheKey);
      }
    },

    /**
     *
     */
    readRemember: function() {
      var vm = this;
      var loginFormInfo = StoreUtil.fetch(vm.loginForm.cacheKey);
      if(!loginFormInfo || !loginFormInfo.username || !loginFormInfo.password) {
        return;
      }
      var secKey = vm.securityKey;

      var username = DesUtil.decode(loginFormInfo.username, secKey);
      var password = DesUtil.decode(loginFormInfo.password, secKey);

      vm.loginForm.username = username;
      vm.loginForm.password = password;
      vm.loginForm.rememberme = loginFormInfo.rememberme || false;
    },

    goto: function(route) {
      this.$router.push(route);
    },

    refreshVerifyCode: function() {
      this.verifyCodeImage = kaptchaImage + "&_=" + new Date().getTime();
    }
  },

  created: function() {
    this.readRemember();
  },

  activated: function() {
    this.readRemember();
  }
};
