import Header from "@/components/Header";
import Footer from "@/components/Footer";

export default {
  name: "unloginMain",
  data() {
    return {
		mainBodyContainerHeight: "auto"
	};
  },
  components: {
    vheader: Header,
    vfooter: Footer
  },

  methods: {
    //tab标签点击时，切换相应的路由
    resize: function() {
      var self = this;
      //页面高
      setTimeout(() => {
        /*var mc = self.$refs.mainContent;
		
		var containerHeight = mc.parentNode.clientHeight;
        //mc.style.height = containerHeight - 2 * 8 * 0 + "px";
		
		self.mainBodyContainerHeight = containerHeight - $("#mainTabContainer").outerHeight() + "px";*/;
		
		var height = document.documentElement.clientHeight;
		//减头部
		height -= $("#mainHeader").outerHeight();
		//减Tab标签
		height -= $("#mainTabContainer").outerHeight();
		//减底部
		height -= $("#mainFooter").outerHeight();
		
		self.mainBodyContainerHeight = height + "px";;
		
      }, 666);
    },

    initTabs: function() {
      var self = this;
      //首页
      var mainRouter = { path: "/main", name: "首页" };
      //当前路由
      var thsRoute = self.$route;
      //初始打开的tab
      //必须首页
      this.$store.commit("add_tabs", mainRouter);
      //
      var activePath = mainRouter.path;
      if (
        thsRoute.path !== "/" &&
        thsRoute.path !== "/home" &&
        thsRoute.path !== mainRouter.path
      ) {
        this.$store.commit("add_tabs", {
          path: thsRoute.path,
          name: thsRoute.name
        });
        activePath = thsRoute.path;
      }
      this.$store.commit("set_active_index", activePath);
    }
  },

  activated: function() {},

  mounted: function() {
    //console.log("home mounted.");

    //this.resize();

    //this.initTabs();
	
	// window.onresize = () => {
	// 	return (() => {
	// 		//console.log("resizing");
	// 		this.resize();
	// 	})()
	// }

  },
  computed: {
  }
    
};
