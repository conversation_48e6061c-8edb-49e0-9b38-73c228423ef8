*.hide {
  display: none;
}

section.content-main {
  padding: 0px;
  background-color: #fff;
  overflow: auto;
}

section.content-main .data-body {
  padding: 0;
}

div.pane-head {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

div.pane-head-label {
  line-height: 30px;
  font-size: 16px;
}

header.main-header {
  /*background-color: #2a579a;*/
  background-color: #2785EB;
}

body.skin-blue .main-header .logo {
  background-color: transparent;
}

body.skin-blue .main-header .logo:hover,
body.skin-blue .main-header .navbar .sidebar-toggle:hover {
  /*background-color: #204f93;*/
  background-color: #2785EB;
}

body.skin-blue .main-header .navbar {
  background-color: transparent;
}

body.skin-blue .wrapper,
body.skin-blue .main-sidebar,
body.skin-blue .left-side {
  /*background-color: #e3e7ec;*/
  background-color: #3B68AA;
}

/*.skin-blue .main-sidebar{
      width:200px;
  }*/

body.skin-blue .user-panel>.info,
body.skin-blue .user-panel>.info>a {
  /*color: #555;*/
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu>li:hover>a,
body.skin-blue .sidebar-menu>li.active>a,
body.skin-blue .sidebar-menu>li.menu-open>a {
  background-color: transparent;
  color: #000;
  color: #EAF6FB;
  font-weight: bold;
  background-color: #3B70B8;
}

body.skin-blue .sidebar-menu>li.treeview>a {
  background-color: #3B70B8;
}

body.skin-blue .sidebar-menu>li>.treeview-menu {
  background-color: #3B68AA;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a,
body.skin-blue .sidebar a {
  color: #555;
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a {
  padding: 8px 6px 8px 28px;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a i {
  margin-right: 4px;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a:hover,
body.skin-blue .sidebar-menu .treeview-menu>li>a.router-link-active {
  color: #000;
  color: #EAF6FB;
}

body.skin-blue .sidebar-menu .treeview-menu>li>a.router-link-active {
  background-color: #fff;
  background-color: #2885EB;
  border-left: 2px solid #A0CEFD;
  padding-left: 26px;
}

ul.treeview-menu {
  padding-left: 0;
}

header.main-header .logo .logo-lg {
  font-weight: 700;
}

body.skin-blue .main-header .navbar .sidebar-toggle {
  height: 50px;
  overflow: hidden;
}

form.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

div.form-row {
  margin: 4px 0;
}

div.form-h-item .el-form-item__content {
  width: 160px;
}

div.form-h-item .el-form-item__label {
  width: 5.2em;
}

div.el-table th {
  background-color: #f2f2f2;
}

div.content-pane {
  margin: 4px;
}

div.paginationClass {
  margin-top: 4px;
}

div.el-tabs__header {
  margin-bottom: 0px;
}

div.el-tabs--card>.el-tabs__header .el-tabs__nav {
  border-left: none;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item {
  background-color: #f4f6f8;
  color: #23508e;
  height: 32px;
  line-height: 32px;
}

/*div.el-tabs--card>.el-tabs__header .el-tabs__item{
      border-bottom-width:0;
  }*/
div.el-tabs--card>.el-tabs__header .el-tabs__item,
div.el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: #e4e7ed;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item.is-active.is-closable {
  background-color: #eaedf1;
  color: #23508e;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item .el-icon-close {
  width: 14px;
}

div.el-tabs--card>.el-tabs__header .el-tabs__item.is-closable:hover {
  padding-left: 20px;
  padding-right: 20px;
}

div.pane-head .el-col:last-child {
  text-align: right;
}

button.tab-button {
  position: relative;
  bottom: -9px;
  margin-left: 0 !important;
  float: left;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

div.search-form-pane {
  max-height: 144px;
  overflow: hidden;
  overflow-y: auto;
}

/*弹出框*/
div.checkFeedbackDialog .el-form-item {
  margin-bottom: 0px;
}

div.editFormDialog div.el-form-item {
  margin-bottom: 8px;
}

div.box-noborder {
  border-width: 0px;
}

div.checkFeedbackDialog .el-dialog__header,
div.checkFeedbackDialog .el-dialog__body {
  padding: 8px;
}

div.checkFeedbackDialog .el-dialog__header {
  background-color: #EFEFEF;
}

div.checkFeedbackDialog .el-dialog__headerbtn {
  top: 9px;
}

div.checkFeedbackDialog .box-footer {
  margin-top: 8px;
  padding: 2px 4px;
  border: 1px solid #f4f4f4;
  border-radius: 6px;
}

div.checkFeedbackDialog .direct-chat-text-mix {
  line-height: 1.6em;
}

div.checkFeedbackDialog .direct-chat-text-tag {
  color: red;
}

div.checkFeedbackDialog .dialog-footer {
  margin: 8px auto;
  text-align: center;
}


div.align-center {
  text-align: center;
}

/*进度条*/
div.mini-step-bar .el-steps--simple {
  background-color: transparent;
  line-height: 1.1;
  padding: 0;
}

div.mini-step-bar .el-step.is-simple {
  flex-basis: auto !important;
  margin-right: 12px !important;
}

div.mini-step-bar .el-step.is-simple .el-step__head {
  padding-right: 2px;
  font-weight: normal;
}

div.mini-step-bar .el-step.is-simple .el-step__title {
  font-size: 12px;
  line-height: 1.1;
  font-weight: normal;
}

div.mini-step-bar .el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: unset;
  word-break: keep-all;
}

div.mini-step-bar .el-step.is-simple .el-step__arrow {
  margin-left: 8px;
}

div.mini-step-bar .el-step.is-simple .el-step__arrow::after,
div.mini-step-bar .el-step.is-simple .el-step__arrow::before {
  height: 8px;
}

div.mini-step-bar .el-step.is-simple .el-step__arrow::after {
  transform: rotate(60deg) translateY(3px);
  top: 1px;
}

div.mini-step-bar .el-step.is-simple .el-step__arrow::before {
  transform: rotate(-60deg) translateY(-3px);
  bottom: 1px;
}

div.mini-step-bar .el-step__line-inner {
  border-width: 0 !important;
}

.current-row>td {
  background: rgba(0, 158, 250, 0.219) !important;
}

.el-table__body tr:hover>td {
  background-color: rgba(0, 158, 250, 0.219) !important;
}

button.el-button--mini, button.el-button--small{
  font-size:14px;
}
#mainTabContainer .el-tabs__item{
  font-size:16px;
}
div.el-table--mini, div.el-table--small{
  font-size:14px;
}
