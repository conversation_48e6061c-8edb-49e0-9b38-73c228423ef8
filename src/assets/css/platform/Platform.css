body.skin-blue .platform-wrapper{
  background-color: #FFF;
}
body h1, body h2, body h3, body h4, body p{
  margin:0;
  padding:0;
}

*.platform-content-wrap {
  margin:0 auto;
  width:1168px;
}

header.platform-header{
  padding:20px 0 25px;
}
h1.platform-title{
  margin:0;
  padding:0;
  font-family: <PERSON><PERSON><PERSON><PERSON>, "华文细黑", "Microsoft YaHei", "微软雅黑", PingFangTC-Semibold;
  font-size: 36px;
  color: #409EFF;
  font-weight: 700;
  letter-spacing: 5.5px
}
div.platform-entry-bar{
  text-align: right;
}
nav.platform-nav{
  background-color:#409EFF;
}
nav.platform-nav-inner{
}
div.platform-index-brief{
  text-align:center;
}
div.platform-index-brief-header-label{
  position: relative;
  display: inline-block;
  width: 145px;
  height: 42px;
  margin: 50px auto 17.5px;
  background: #b93337;  
  color:#FFF;
}
div.platform-index-brief-header-label:before {
    content: "";
    position: absolute;
    left: -12.1px;
    top: 0;
    border-width: 21px 12.2px 21px 0;
    border-style: solid;
    border-color: transparent #b93337 transparent transparent;
}
div.platform-index-brief-header-label:after {
    content: "";
    position: absolute;
    right: -12.1px;
    top: 0;
    border-width: 21px 0 21px 12.2px;
    border-style: solid;
    border-color: transparent transparent transparent #b93337;
}
div.platform-index-brief-content{
  padding-top:16px;
}
div.platform-index-brief-content p{
  text-indent: 2em;
  text-align: left;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #111;
  letter-spacing: 0;
  line-height: 26px;
  padding: 0 59.5px;
}
div.platform-index-data-overview{
  padding-top:48px;
}
div.platform-register{
  min-height: 640px;
}
div.platform-register-breadcrumb{
  padding:7px 0;
}

div.platform-register-steps .el-steps--simple{
  background-color: transparent;
}
div.platform-register-steps .el-step__head{
  display: none;
}
span.platform-register-step-icon{
  margin-right:2px;
}
div.platform-register-steps .el-step__arrow{
  border-top: 1px dashed #DDD;
  height:1px;
  margin:10px 8px 0; 
}
div.platform-register-steps .el-step.is-simple .el-step__arrow::before
, div.platform-register-steps .el-step.is-simple .el-step__arrow::after{
  display: none;
}
div.platform-register-content{
  box-shadow: 0 2px 13px 0 rgba(0,0,0,.16);
}
div.platform-register-content-guide{
  padding: 16px 16px 0;
}
div.platform-register-content-guide h4{
  font-size:16px;
}
div.platform-register-content-bar{
  padding: 16px 0;
}
div.platform-register-content-bar-buttons{
  text-align: center;
}
div.platform-register-content-bar-check{
  padding-top: 14px;
}

footer.platform-footer{
  padding:16px 0;
  min-height: 225px;
  /*background: url("../../assets/res/platform/footer-bg.png");*/
  background-color:#409EFF;
  line-height:2;
}
footer.platform-footer *{
  color: #FFF;
}
span.platform-footer-col-label-dot{
  display: inline-block;
  margin-right: 13px;
  border: 3px solid #fff;
  border-radius: 100%;
}
div.platform-footer-col-detail{
  padding-left:16px;
}
div.platform-footer-col-links a{
  display:inline-block;
  width:48%;
  word-break: keep-all;
  white-space: nowrap;
}
div.platform-footer-col-links a:hover, div.platform-footer-col-links a:visited {
  color: #EFEFEF;
}
div.platform-footer-copyright{
  text-align: center;
}

input.el-upload__input {
  display: none!important;
}
