<template>
  <aside class="main-sidebar">
    <!-- sidebar: style can be found in sidebar.less -->
    <section class="sidebar">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel">
        <div class="pull-left image">
          <img :src="user.avatar" class="img-circle" alt="User Image">
        </div>
        <div class="pull-left info">
          <p>{{ user.name }}</p>
          <p>{{ user.org }}</p>
          <!-- Status -->
          <!-- <span>
            <i class="fa fa-circle text-success"></i> 在线
          </span> -->
        </div>
      </div>

      <!-- search form (Optional)
	  <form action="#" method="post" class="sidebar-form">
		<div class="input-group">
		  <input type="text" name="q" class="form-control" placeholder="搜索...">
		  <span class="input-group-btn">
			  <button type="submit" name="search" id="search-btn" class="btn btn-flat"><i class="fa fa-search"></i>
			  </button>
			</span>
		</div>
      </form>-->
      <!-- /.search form -->

      <navmenu></navmenu>
    </section>
    <!-- /.sidebar -->
  </aside>
</template>

<style type="text/css">
.main-sider {
  background-color: #e3e7ec;
}

.user-panel > .info,
.user-panel > .info > a {
  color: #555;
}

.skin-blue .sidebar-menu > li.header {
  background-color: transparent;
}
</style>

<script>
import global from "@/config/global-config";
import NavMenu from "@/components/NavMenu";
import LoginHelper from "@/utils/LoginHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  //name: 'app',
  data() {
    return {
      user: {
        name: "-",
        jobTitle: "-",
        org:'-',
        avatar: global.userAvatar
      }
    };
  },
  components: {
    navmenu: NavMenu
  },
  mounted: function() {
    var self = this;

    var userInfo = LoginHelper.logincheck({attach: "company"});
    self.user.name = userInfo ? userInfo.userName : "-";
    self.user.org  = userInfo && userInfo["company"] ? userInfo["company"]["companyName"]:"-";
    self.user.avatar = userInfo ? userInfo.avatarUrl : self.user.avatar;
  }
};
</script>