<template>
<div class="wrapper" style="background-color:#e3e7ec">
	<div id="app">
	
    <div class="login-container">
        <el-form :model="loginForm" :rules="loginRule"
         status-icon
         ref="loginForm" 
         label-position="left" 
         label-width="0px" 
         class="login-page" size="medium">
        <h3 class="title">{{title}}</h3>
            <el-form-item prop="username">
                <el-input type="text" 
                    v-model="loginForm.username" 
                    auto-complete="off" 
                    placeholder="用户名"
                    @keyup.enter.native="handleSubmit"
                ></el-input>
            </el-form-item>

            <el-form-item prop="password">
                <el-input type="password" 
                    v-model="loginForm.password" 
                    auto-complete="off" 
                    placeholder="密码"
                    @keyup.enter.native="handleSubmit"
                ></el-input>
            </el-form-item>

            <el-form-item prop="verifyCode">
                <el-input type="text" style="width:62%;" 
                    v-model="loginForm.verifyCode" 
                    auto-complete="off" 
                    placeholder="验证码"
                    @keyup.enter.native="handleSubmit"
                ></el-input>
                <el-image :src="verifyCodeImage" @click="refreshVerifyCode"
                 style="vertical-align:middle;min-width:80px;min-height:24px" title="点击更新验证码" />
            </el-form-item>

            <el-form-item style="margin-bottom:0">
              <el-row>
                <el-col :span="12">
                  <el-checkbox 
                    v-model="loginForm.rememberme"
                    class="rememberme"
                  >记住密码</el-checkbox>
                </el-col>
                <el-col :span="12" style="text-align: right;">
                  <a href="javascript://" @click="goto({name: '重置密码'})">重置密码</a>
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item style="width:100%;margin-bottom:12px;" size="medium">
                <el-button type="primary" style="width:100%;" @click="handleSubmit" :loading="logining">登录</el-button>
            </el-form-item>

<!--            <el-form-item style="width:100%;" size="medium">
              <el-button-group style="width:100%;">
                <el-button type="success" style="width:50%;" @click="goto({name: '平台首页'})">平台首页</el-button>
                <el-button type="success" style="width:50%;" @click="goto({name: '注册须知'})">申请加入</el-button>
              </el-button-group>
            </el-form-item>-->
        </el-form>
    </div>

	</div>
</div>

</template>

<script>
import '@/assets/css/Login.css';

import model from '@/assets/js/Login.js';
export default model;
</script>

