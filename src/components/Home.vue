<template>
  <div class="wrapper">
    <div id="app">
      <!-- 顶部 -->
      <vheader/>

      <!-- 侧栏, 用户在线状态, 菜单 -->
      <vsider/>

      <!-- 主体内容 -->
      <div id="contentWrapper" class="content-wrapper">
        <section ref="mainContent" class="content container-fluid content-main">
          <div id="mainTabContainer">
            <el-tabs
              v-model="activeIndex"
              size="small"
              type="card"
              v-if="openTab.length"
              @tab-click="tabClick"
              @tab-remove="tabRemove"
            >
              <el-tab-pane
                v-for="(item) in openTab"
                :key="item.name"
                :label="item.name"
                :name="item.path"
                :closable="'/main' != item.path"
              ></el-tab-pane>
            </el-tabs>
          </div>

          <div
            id="mainBodyContainer"
            class="content-wrap"
            :style="{overflow: 'auto', height: mainBodyContainerHeight}"
          >
            <keep-alive :include="cacheComponents"> 
              <router-view></router-view>
            </keep-alive>
          </div>
        </section>
        <!-- /.content -->
      </div>
      <!-- /.content-wrapper -->

      <!-- 底部 -->
      <vfooter/>
    </div>
  </div>
</template>

<script>
import '@/assets/css/Home.css';

import me from "@/assets/js/Home";

export default me;
</script>

<style>
</style>
