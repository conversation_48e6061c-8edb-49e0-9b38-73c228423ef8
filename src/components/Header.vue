<template>
  <header id="mainHeader" class="main-header">
    <!-- Logo -->
    <a href="javascript://" class="logo">
      <!-- mini logo for sidebar mini 50x50 pixels -->
      <span class="logo-mini">
        <b>地贫上报</b>
      </span>
      <!-- logo for regular state and mobile devices -->
      <span class="logo-lg">{{title}}</span>
    </a>

    <!-- Header Navbar -->
    <nav class="navbar navbar-static-top" role="navigation">
      <!-- Sidebar toggle button-->
      <a href="#" class="sidebar-toggle" data-toggle="push-menu" role="button">
        <span class="sr-only">Toggle navigation</span>
      </a>
      <!-- Navbar Right Menu -->
      <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
          <!-- Messages: style can be found in dropdown.less-->
          <li class="dropdown messages-menu">
            <!-- Menu toggle button - ->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-envelope-o"></i>
              <span class="label label-success">1</span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">您有1条未读消息</li>
              <li>
                <!- - inner menu: contains the messages - ->
                <ul class="menu">
                  <li>
                    <!- - start message - ->
                    <a href="#">
                      <div class="pull-left">
                        <!- - User Image - ->
                        <img src="static/img/user2-160x160.jpg" class="img-circle" alt="User Image">
                      </div>
                      <!- - Message title and timestamp - ->
                      <h4>
                        测试组
                        <small>
                          <i class="fa fa-clock-o"></i> 5 分钟前
                        </small>
                      </h4>
                      <!- - The message - ->
                      <p>新测试任务</p>
                    </a>
                  </li>
                  <!- - end message - ->
                </ul>
                <!- - /.menu - ->
              </li>
              <li class="footer">
                <a href="#">所有消息</a>
              </li>
            </ul>
          </li>
            <!- - /.messages-menu-->

            <!-- Notifications Menu - ->
          </li>
          <li class="dropdown notifications-menu">
            <!- - Menu toggle button - ->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-bell-o"></i>
              <span class="label label-warning">0</span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">您有0个提醒</li>
              <li>
                <!- - Inner Menu: contains the notifications - ->
                <ul class="menu">
                  <li>
                    <!- - start notification - ->
                    <a href="#">
                      <i class="fa fa-users text-aqua"></i> 0 条代办提醒
                    </a>
                  </li>
                  <!- - end notification - ->
                </ul>
              </li>
              <li class="footer">
                <a href="#">所有提醒</a>
              </li>
            </ul>
          </li>
            <!- - /.Notifications Menu-->

            <!-- Tasks Menu -->
          </li>
          <li class="dropdown tasks-menu" v-if="false">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-flag-o"></i>
              <span class="label label-danger">1</span>
            </a>
            <ul class="dropdown-menu">
              <li class="header">您有1个任务</li>
              <li>
                <!-- Inner menu: contains the tasks -->
                <ul class="menu">
                  <li>
                    <!-- Task item -->
                    <a href="#javascript:" @click="toUploadLog">
                      <!-- Task title and progress text -->
                      <h3>
                        2019年第1季度指标填报完成度
                        <small class="pull-right">1/3</small>
                      </h3>
                      <!-- The progress bar -->
                      <div class="progress xs">
                        <!-- Change the css width attribute to simulate progress -->
                        <div
                          class="progress-bar progress-bar-aqua"
                          style="width: 33%"
                          role="progressbar"
                          aria-valuenow="33"
                          aria-valuemin="0"
                          aria-valuemax="100"
                        >
                          <span class="sr-only">2019年第1季度指标填报完成度 1/3</span>
                        </div>
                      </div>
                    </a>
                  </li>
                  <!-- end task item -->
                </ul>
              </li>
              <li class="footer">
                <a href="javascript:" @click="toUploadLog">所有任务</a>
              </li>
            </ul>
          </li>
          <!-- User Account Menu -->
          <li class="dropdown user user-menu">
            <!-- Menu Toggle Button -->
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <!-- The user image in the navbar-->
              <img :src="user.avatar" class="user-image" alt="User Image">
              <!-- hidden-xs hides the username on small devices so only the image appears. -->
              <span class="hidden-xs">{{ user.name }}</span>
              <span class="hidden-xs">[{{ user.org }}]</span>
            </a>
            <ul class="dropdown-menu">
              <li class="mt5">
                <a href="javascript:" @click="viewUserInfo">
                  <i class="fa fa-user"></i> 个人中心
                </a>
              </li>
              <li>
                <a href="javascript:" @click="toChangePassword">
                  <i class="fa fa-key"></i> 修改密码
                </a>
              </li>
              <li class="divider"></li>
              <li>
                <a href="javascript:" v-on:click="logout">
                  <i class="fa fa-sign-out"></i> 退出登录
                </a>
              </li>
              <li class="mt10"></li>
            </ul>
          </li>
        </ul>
      </div>
    </nav>
  </header>
</template>

<style type="text/css">
.navbar-nav > .user-menu > ul.dropdown-menu {
  padding: 6px 0;
  width: 160px;
  background-color: #fff;
  box-shadow: 0px 2px 8px 1px #DDD;
}
.navbar-custom-menu > .navbar-nav > li > ul.dropdown-menu {
  right: 6px;
}
ul.dropdown-menu > li > a {
  padding: 5px 20px;
}
</style>

<script>
import globalVariables from "@/config/global-config";
import LoginHelper from "@/utils/LoginHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  data() {
    return {
      title: globalVariables.productName,
      user: {
        name: "-",
        jobTitle: "-",
        org:"-",
        avatar: globalVariables.cmis.domain + globalVariables.userAvatar
      }
    };
  },
  methods: {
    viewUserInfo: function() {
      this.$router.push({ path: "/userInfo" });
    },
    toChangePassword: function() {
      this.$router.push({ path: "/changePassword" });
    },
    logout() {
      LoginHelper.logout(this.$router);
    },
    toUploadLog() {
      if (1 == formConfig.value_user.org_id) {
        this.$router.push({ path: "/uploadLogManager" });
      } else {
        this.$router.push({ path: "/uploadLog" });
      }
    },

    setupPage: function() {
      var self = this;
      
      var userInfo = LoginHelper.logincheck({attach: "company"});
      self.user.name = userInfo ? userInfo.userName : "-";
      self.user.org  = userInfo && userInfo["company"] ? userInfo["company"]["companyName"]:"-";
      self.user.avatar = userInfo ? userInfo.avatarUrl : "-";
    }
  },
  
  mounted: function() {
    this.setupPage();
  },

  activated: function() {

    this.setupPage();
  }
};
</script>

