<template>
  <div>
    <el-container>
      <el-main class="data-body" :class="{'data-body-nested': 'standalone' != editMode}">
        <div>
          <el-row class="pane-head" v-if="'register' != editMode">
            <el-col :span="21" class="pane-head-label">
              <i class="el-icon-menu"></i>
              &nbsp;{{headerTitle}}
            </el-col>
            <el-col :span="3">&nbsp;</el-col>
          </el-row>
        </div>
        <div class="content-pane" :style="styles[editMode]">
          <el-form
            ref="mainForm"
            :model="form"
            :label-width="'register' == editMode?'360px':'180px'"
            :rules="'nested' == editMode?null:formRules"
            :disabled="'nested' == editMode"
          >
            <div class="form-row-button">
              <el-button
                type="primary"
                @click="confirmToSaveData"
                size="mini"
                :loading="loading"
                v-if="'standalone' == editMode"
              >更新</el-button>
              <span
                @click="$router.push({name:'个人中心'})"
                class="el-button el-button--primary el-button--mini"
                v-if="'nested' == editMode"
              >进入个人中心</span>
            </div>

            <el-row class="form-row">
              <el-col :span="spans[editMode]['pic']" :offset="spans[editMode]['offset1']">
                <div class="lft-col">
                  <el-form-item>
                    <img :src="form.avatar" :alt="form.userName" />
                  </el-form-item>
                  <el-form-item>
                    <span>{{form.userName || '&nbsp;'}}</span>
                  </el-form-item>
                  <el-form-item prop="sex">
                    <el-radio-group v-model="form.sex" v-on:change="changeSex">
                      <el-radio label="1">男</el-radio>
                      <el-radio label="2">女</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="!isNested">
                    <el-button type="primary" size="mini" disabled title="暂不支持">修改头像</el-button>
                  </el-form-item>
                </div>
              </el-col>

              <el-col :span="spans[editMode]['edit']" :offset="spans[editMode]['offset2']">
                <div id="user" >
                <el-form-item label="所属单位" prop="corpCode" v-if="'register' == editMode">
                  <el-cascader
                    v-model="form.corpCode"
                    placeholder="试试搜索"
                    :props="{value: 'id', label: 'dictLabel'}"
                    :options="form.combo_orgId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="用户姓名" prop="userName">
                  <el-input type="text" v-model="form.userName" :placeholder="'register' == editMode?formRules['userName'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.mobile[editMode]" prop="mobile">
                  <el-input type="text" v-model="form.mobile" :placeholder="'register' == editMode?formRules['mobile'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.phone[editMode]" prop="phone">
                  <el-input type="text" v-model="form.phone" :placeholder="'register' == editMode?formRules['phone'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.email[editMode]" prop="email">
                  <el-input type="text" v-model="form.email" :placeholder="'register' == editMode?formRules['email'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.extendS4[editMode]" prop="extendS4">
                  <el-input type="text" v-model="form.extendS4" :placeholder="'register' == editMode?formRules['extendS4'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.extendS5[editMode]" prop="extendS5">
                  <el-input type="text" v-model="form.extendS5" :placeholder="'register' == editMode?formRules['extendS5'][0]['message']:''"></el-input>
                </el-form-item>

                <el-form-item :label="labels.extendS6[editMode]" prop="extendS6">
                  <el-input type="text" v-model="form.extendS6" :placeholder="'register' == editMode?formRules['extendS6'][0]['message']:''"></el-input>
                </el-form-item>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
div.lft-col {
  margin: 0 auto;
  width: 90%;
  text-align: center;
}
div.lft-col .el-form-item__content {
  margin-left: 0 !important;
}
div.box-noborder {
  border-top-width: 0;
  border-width: 0;
}

div.form-row-button {
  padding: 8px 0;
  text-align: right;
}
body main.data-body-nested {
  padding-left: 0;
  padding-right: 0;
}
</style>

<script>
import global from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";
import LoginHelper from "@/utils/LoginHelper";

import formConfig from "@/config/uploadLog-config";
import {validateEMail,validatePhone,validateTelphone} from "@/utils/ValidatorUtil";

export default {
  name: "page_userInfo",

  data() {
    return {
      loading: false,

      form: {
        userName: "",
        email: "",
        mobile: "",
        phone: "",
        sex: "2",
        avatar: global.cmis.domain + global.userAvatarWomen,
        corpCode: null,
        extendS4: null,
        extendS5: null,
        extendS6: null,

        combo_orgId: []
      },
      spans:{
        register:{pic:6,edit:18,offset1:0,offset2:0},
        standalone:{pic:6,edit:10,offset1:0,offset2:0},
        nested:{pic:6,edit:18,offset1:0,offset2:0}  
      },
      styles:{
        register:{margin:'0 auto',width:'90%','min-width':'1024px'},
        standalone:{margin:'0 auto',width:'80%','min-width':'680px'},
        nested:{margin:'0 auto',width:'80%','min-width':'680px'}
      },
      labels:{
        email:{register:"医院护理部主任（数据填报负责人）电子邮箱",standalone:"第一管理员电子邮箱",nested:"第一管理员电子邮箱"},
        mobile:{register:"医院护理部主任（数据填报负责人）手机",standalone:"第一管理员手机",nested:"第一管理员手机"},
        phone:{register:"医院护理部主任（数据填报负责人）办公电话",standalone:"第一管理员办公电话",nested:"第一管理员办公电话"},
        extendS4:{register:"医院数据管理员（数据填报人）手机",standalone:"第二管理员手机",nested:"第二管理员手机"},
        extendS5:{register:"医院数据管理员（数据填报人）办公电话",standalone:"第二管理员办公电话",nested:"第二管理员办公电话"},
        extendS6:{register:"医院数据管理员（数据填报人）电子邮箱",standalone:"第二管理员电子邮箱",nested:"第二管理员电子邮箱"}
      },
      avatars:{
        1:global.cmis.domain + global.userAvatar,
        2:global.cmis.domain + global.userAvatarWomen
      },
      formRules: {
        corpCode:[{ required: true, message: "请选择所属单位" }],
        userName: [
          { required: true, message: "请填写用户姓名", trigger: "blur" }
        ],
        email: [{ required: true, message: "请填写第一管理员电子邮箱", trigger: "blur" },
        {validator:validateEMail,trigger:'blur'}
        ],
        mobile: [
          { required: true, message: "请填写第一管理员手机", trigger: "blur" },
          {validator:validatePhone,trigger:'blur'}
        ],
        phone: [
          { required: true, message: "请填写第一管理员办公电话", trigger: "blur" },
          {validator:validateTelphone,trigger:'blur'}
        ],
        sex: [{ required: true, message: "请选择性别" }],
        extendS6:[{ required: false, message: "请填写第二管理员电子邮箱" },
        {validator:validateEMail,trigger:'blur'}],
        extendS4:[{ required: false, message: "请填写第二管理员手机" },
        {validator:validatePhone,trigger:'blur'}],
        extendS5:[{ required: false, message: "请填写第二管理员办公电话" },
        {validator:validateTelphone,trigger:'blur'}]
      }
    };
  },

  props: {
    editMode: { default: "standalone" },
    headerTitle: { default: "个人中心" }
  },

  mounted: function() {
    var vm = this;
    if ("register" == vm.editMode) {
      //注册要求选医院
      // vm.formRules["corpCode"] = [
      //   { required: true, message: "请选择所属单位" }
      // ];
      for(var item in vm.labels)
      {
        if(vm.formRules.hasOwnProperty(item))
        {
          vm.formRules[item][0].message = '请填写'+vm.labels[item].register;
          vm.formRules[item][0].required = true;
        }
          
      }
      //
      vm.findOrgHire();
    } else {
      vm.readData();
    }
  },

  methods: {
    confirmToSaveData: function() {
      var self = this;

      self
        .$confirm("是否确定保存?", "确认")
        .then(() => {
          self.saveData();
        })
        .catch(() => {});
    },
    changeSex:function(label) {
      var self=this;
      if(label=='1' || label=='2')
        this.form.avatar=this.avatars[label];
    },
    saveData: function(opts) {
      var self = this;

      self.$refs["mainForm"].validate(valid => {
        if (!valid) {
          return;
        }
        //
        var formData = {};
        formData["userName"] = self.form.userName;
        formData["email"] = self.form.email;
        formData["mobile"] = self.form.mobile;
        formData["phone"] = self.form.phone;
        formData["sex"] = self.form.sex;
        formData["avatar"] = self.form.avatar;
        formData["extend.extendS4"] = self.form.extendS4;
        formData["extend.extendS5"] = self.form.extendS5;
        formData["extend.extendS6"] = self.form.extendS6;
        var corpCode = self.form.corpCode;
        formData["corpCode"] =
          corpCode && corpCode.length ? corpCode[corpCode.length - 1] : null;

        var ajaxOpts = {
          url: global.cmis.domain + "/sso/auth/updateUserInfo",
          type: "post",
          dataType: "json",
          data: formData,
          success: function(res) {
            self.loading = false;
            //console.log(res);
            //其他回调
            var callback = opts ? opts["successHandler"] : null;
            if ("function" == $.type(callback)) {
              callback(res, res["data"] || formData);
            } else {
              //提示信息
              self.$alert(res["message"]);
            }
          },
          error: function() {
            self.loading = false;
          }
        };

        self.loading = true;

        AjaxUtil.send(ajaxOpts, true);
      });
    },

    readData: function() {
      var self = this;

      //成功
      var ui = LoginHelper.logincheck();

      self.updateData(ui);
    },

    updateData(ui) {
      var self = this;

      ui = ui || {};
      self.form.userName = ui["userName"] || "";
      self.form.email = ui["email"] || "";
      self.form.mobile = ui["mobile"] || "";
      self.form.phone = ui["phone"] || "";
      self.form.sex = ui["sex"] || "";
      self.form.avatar = (ui["avatarUrl"] || global.userAvatar).replace("/ctxPath", global.cmis.domain);
      var extend = ui["extend"];
      if(extend) {
        self.form.extendS4 = extend["extendS4"];
        self.form.extendS5 = extend["extendS5"];
        self.form.extendS6 = extend["extendS6"];
      }
    },

    findOrgHire: function() {
      var vm = this;
      var ajaxOpts = {
        url: global.cmis.domain + "/carePlatform/dataChange/findHireData",
        type: "post",
        dataType: "json",
        data: { dictType: "organization", "extend.extendD1": "0" },
        success: function(res) {
          var rows = res ? res["data"] : null;

          vm.form.combo_orgId = rows || [];
        }
      };

      self.loading = true;

      $.ajax(ajaxOpts);
    }
  },

  computed: {
    isNested: function() {
      return !!this.nested;
    }
  }
};
</script>