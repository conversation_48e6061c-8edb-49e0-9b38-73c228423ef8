<template>
  <!-- 主体  -->
  <component :is="view"></component>
</template>

<style type="text/css">
</style>

<script>

import formConfig from "@/config/uploadLog-config";
import UploadLog from "@/components/pages/uploadLog";
import UploadLogManager from "@/components/pages/uploadLogManager";

export default {
  name: "page_main",
  
  components: {
    vUploadLog: UploadLog,
    vUploadLogManager: UploadLogManager
  },
  data() {
    return {
      view: null
    };
  },

  mounted: function() {
    var self = this;
    //如果是广西质控中心就可以编辑<component :is="view"></component>
    setTimeout(function() {

      if (1 == formConfig.value_user.org_id) {
        self.view = "vUploadLogManager";
      } else {
        self.view = "vUploadLog";
      }
    }, 668);
  }
};
</script>
