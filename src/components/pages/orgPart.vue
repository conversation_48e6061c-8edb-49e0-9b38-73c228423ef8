<template>
  <div>
    <el-container :style="{height: mainContainerHeight + 'px'}">
      <el-aside class="tree-pane">
        <el-tree
          ref="orgPartTree"
          :props="orgPartProps"
          node-key="id"
          :default-expanded-keys="[1]"
          :load="loadNodes"
          lazy
          :expand-on-click-node="true"
          @current-change="handleNodeChange"
        ></el-tree>
      </el-aside>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="20" class="pane-head-label">
              <i class="el-icon-menu"></i>
              病区维护 :
              <strong>{{selectedNodeData["treeNames"]}}</strong>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                size="mini"
                @click="handleEdit"
                :disabled="!selectedNodeData.parentCode || !selectedNodeData.extend || !selectedNodeData.extend.extendF1"
              >添加病区名称</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="content-pane">
          <div class="dataTableContainer">
            <el-table
              :data="tableData"
              style="width: 536px;"
              highlight-current-row
              stripe
              :show-header="false"
              size="mini"
            >
              <el-table-column type="index" width="50"/>
              <el-table-column label="病区名称" prop="dictLabelOrig" width="320"/>
              <el-table-column label="操作" width="160" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.extend && user['orgId'] == scope.row.extend.extendS2">
                    <el-button type="text" size="small" 
                      @click="handleEdit(scope.row)"
                    >编辑</el-button>
                    <el-button type="text" size="small" 
                      @click="handleDelete(scope.row)"
                    >删除</el-button>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      title="编辑病区名称"
      :visible.sync="orgPartForm.visible"
      slot="footer"
      class="dialog-popup"
      width="600px"
      :close-on-click-modal="false"
    >
      <div style="display:none">
        <el-input type="hidden" v-model="orgPartForm.id"></el-input>
        <el-input type="hidden" v-model="orgPartForm.code"></el-input>
      </div>
      <el-form :model="orgPartForm">
        <el-form-item label="上级病区:" size="mini">
          {{selectedNodeData['treeNames']}}
          <!-- <el-input :value="selectedNodeData['treeNames']" disabled></el-input> -->
        </el-form-item>
        <el-form-item label="病区编码" size="mini" v-if="formVisible">
          <el-input v-model="orgPartForm.value"></el-input>
        </el-form-item>
        <el-form-item label="病区名称" size="mini">
          <el-input v-model="orgPartForm.name"></el-input>
        </el-form-item>
        <el-form-item label="病区排序" size="mini" v-if="formVisible">
          <el-input v-model="orgPartForm.sort"></el-input>
        </el-form-item>
        <el-row>
          <el-col :span="20" class="align-center">&nbsp;</el-col>
          <el-col :span="4" class="align-center">
            <el-button size="mini" type="primary" @click="save">提交</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<style type="text/css" scoped>
aside.tree-pane{
  width: 320px!important;
  border-right: 1px solid #DDD;
  background-color: #FFF;
}
div.dialog-popup .el-dialog__header,
div.dialog-popup .el-dialog__body {
  padding: 8px;
}
div.align-center {
  text-align: center;
}
</style>

<script>
import global from "@/config/global-config";
import MQUtil from "@/utils/MQUtil";

import LayoutUtil from "@/utils/PageUtil";
import AjaxUtil from "@/utils/AjaxUtil";

import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_orgPart",
  
  data() {
    return {
      mainContainerHeight: 486,
      formVisible: false,
      tableData: [],

      user: {
        orgId: formConfig["value_user"]["org_id"]
      },

      orgPartProps: {
        label: this.treeNodeLabel,
        isLeaf: this.treeNodeIsLeaf
      },

      selectedNodeData: {},

      orgPartForm: {
        visible: false,

        id: "",
        code: "",
        value: "",
        name: "",
        sort: ""
      }
    };
  },

  mounted: function() {
    var self = this;

    self.layoutPage();

    var wresfx = window.onresize;
    window.onresize = function() {
      if (wresfx) {
        wresfx();
      }
      self.layoutPage();
    };
  },

  methods: {
    layoutPage: function() {
      setTimeout(() => {
        this.mainContainerHeight = LayoutUtil.mainBodyHeight();
      }, 688);
    },
    /**
     * 加载子节点
     */
    loadNodes(node, resolve) {
      var postData = {};
      var nodeData = node["data"];
      if (nodeData && nodeData["dictCode"]) {
        postData["parentCode"] = nodeData["dictCode"];
      }

      var ajaxOpts = {
        type: "post",
        url: global.cmis.domain + "/carePlatform/orgPart/findList?numChildren=-1",
        data: postData,
        dataType: "json",
        success: function(res) {
          var nodesData = res && res.data || [];
          /*for(var i = 0, row; i < nodesData.length; i ++) {
            row = nodesData[i];
            if(!row.numChildren) {
              row["leaf"] = true;
            }
          }*/
          resolve(nodesData);
        },
        error: function() {
          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 切换节点触发
     */
    handleNodeChange: function(nodeData, node) {
      this.selectedNodeData = nodeData;

      this.loadNodes(node, this.updateTableData);
    },
    /**
     * 更新表格数据
     */
    updateTableData: function(data) {
      this.tableData = data;
    },
    /**
     * 编辑记录
     */
    handleEdit: function(row) {
      var frm = this.orgPartForm;
      frm.visible = true;

      row = row || {};
      frm.id = row["id"] || "";
      frm.parentCode = this.selectedNodeData["dictCode"] || "";
      frm.code = row["dictCode"] || "";
      frm.value = row["dictValue"] || "";
      frm.name = row["dictLabelOrig"] || "";
      frm.sort = row["treeSort"] || "1";
    },
    /**
     * 保存
     */
    save: function() {
      var self = this,
        frm = self.orgPartForm;
      //提交的数据
      var postData = {
        id: frm.id || "",
        parentCode: self.selectedNodeData["dictCode"] || "",
        dictCode: frm.code || "",
        dictLabelOrig: frm.name || "",
        dictValue: frm.name || "",
        treeSort: frm.sort || "1",
        isSys: 0
      };

      var ajaxOpts = {
        type: "post",
        url: global.cmis.domain + "/carePlatform/orgPart/save",
        data: postData,
        dataType: "json",
        success: function(res) {
          var m = (res && res["message"]) || "保存完成";
          self.$alert(m);

          if (res && "true" == res.result) {
            frm.visible = false;
            //
            var tree = self.$refs.orgPartTree;
            var nodeData = res.data,
              node = tree.getNode(nodeData);
            //编辑或新增节点
            if (node) {
              node.data = nodeData;
            } else {
              //更新父节点子节点数
              var numChildren = parseInt(self.selectedNodeData["numChildren"]);
              numChildren = isNaN(numChildren)? 0 : numChildren;
              self.selectedNodeData["numChildren"] = ++numChildren;
              tree.getNode(self.selectedNodeData).data = self.selectedNodeData;
              //树添加子节点
              tree.append(nodeData, self.selectedNodeData);
            }
            //更新
            self.refresh();
          }
        },
        error: function() {
          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 节点标签
     */
    treeNodeLabel: function(data, node) {
      //名称(子节点数)
      return (data["dictLabel"] || data["dictLabelOrig"]) + "(" + (data["numChildren"] || 0) + ")";
    },
    /**
     * 是否叶子节点
     */
    treeNodeIsLeaf: function(data, node) {
      //没有子节点为叶子节点
      var numChildren = parseInt(data["numChildren"]);
      return isNaN(numChildren) || numChildren <= 0;
    },

    /**
     * 删除记录
     */
    handleDelete: function(row) {
      var vm = this;
      var nodeId = row["id"];
      vm.$confirm('是否确定删除?').then(() => {
        AjaxUtil.get(global.cmis.domain + "/carePlatform/orgPart/del?dictType=" + row["dictType"] + "&id=" + nodeId, function(res) {
          
          vm.$alert("删除成功.");
          //
          vm.$refs.orgPartTree.remove({"id": nodeId});
          //更新
          vm.refresh();
        });
      }).catch($.noop);
      
    },

    /**
     * 更新树
     */
    refresh: function() {
        //更新树节点下子节点列表
        this.loadNodes({ data: this.selectedNodeData }, this.updateTableData);
        //更新缓存
        formConfig.loadData(null, true);
        //
        MQUtil.sendMessage("orgPart", "lastUpdated", new Date().getTime());

    }
  }
};
</script>

