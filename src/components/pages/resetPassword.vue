<template>
<div class="wrapper" style="background-color:#e3e7ec">
  <div id="app">
  
    <div class="login-container">
        <el-form :model="form" :rules="formRules"
         status-icon
         ref="mainForm" 
         label-position="left" 
         label-width="0px" 
         class="login-page" size="medium">
        <h3 class="title">{{title}}</h3>
            <el-form-item prop="mobile">
                <el-input type="text" 
                    v-model="form.mobile" 
                    auto-complete="off" 
                    placeholder="登录账号或手机"
                ></el-input>
            </el-form-item>

            <el-form-item prop="smsCode">
                <el-input type="text" 
                    v-model="form.smsCode" 
                    auto-complete="off" 
                    placeholder="短信验证码"
                    style="width:62%;"
                ></el-input>
                <el-button type="primary" style="width:36%;" @click="requireSmsCode" :loading="logining">获取验证码</el-button>
            </el-form-item>

            <el-form-item prop="newPassword">
                <el-input type="password" 
                    v-model="form.newPassword" 
                    auto-complete="off" 
                    placeholder="新密码"
                ></el-input>
            </el-form-item>

            <el-form-item prop="confirmNewPassword">
                <el-input type="password" 
                    v-model="form.confirmNewPassword" 
                    auto-complete="off" 
                    placeholder="确认新密码"
                ></el-input>
            </el-form-item>

            <el-form-item style="width:100%;margin-bottom:12px;" size="medium">
                <el-button type="primary" style="width:100%;" @click="confirmTodo" :loading="logining">重置密码</el-button>
            </el-form-item>

            <el-form-item style="width:100%;" size="medium">
              <el-button-group style="width:100%;">
                <el-button type="success" style="width:50%;" @click="goto({name: '平台首页'})">平台首页</el-button>
                <el-button type="success" style="width:50%;" @click="goto({name: '登录'})">登录平台</el-button>
              </el-button-group>
            </el-form-item>
        </el-form>
    </div>

  </div>
</div>

</template>

<script>
import '@/assets/css/Login.css';

import model from "@/assets/js/pages/resetPassword";
export default model;
</script>
