<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div>
          <el-row class="pane-head">
            <el-col :span="6" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;指标完整率
            </el-col>
            <el-col :span="8">
              <el-button size="mini" :type="tabs.onQuarter" class="tab-button">按季度</el-button>
              <el-button size="mini" :type="tabs.onYear" class="tab-button">按年度</el-button>
            </el-col>
            <el-col :span="10">
              <el-button size="mini">导出</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="地区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :props="form.props"
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="开始时间" class="form-h-item">
                  <el-select v-model="form.value_fromYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="结束时间" class="form-h-item">
                  <el-select v-model="form.value_toYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度" class="form-h-item">
                  <el-select v-model="form.value_toQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="上报国家" class="form-h-item">
                  <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="终审状态" class="form-h-item">
                  <el-select v-model="form.value_checkState" placeholder="通过" size="mini">
                    <el-option
                      v-for="item in form.combo_checkState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位定级" class="form-h-item">
                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="tb-edit"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            @row-click="handleRowClick"
            v-loading="loading"
          >
            <el-table-column label="序号" type="index" width="50" fixed/>
            <el-table-column label="省份" prop="provinceName" width="160" fixed/>
            <el-table-column label="地市" prop="areaName" width="160" fixed/>
            <el-table-column label="采集范围" prop="orgPart" width="160" fixed/>
            <el-table-column label="医院定级" prop="orgGrade" width="160"/>
            <el-table-column label="填报季度" prop="timeStr" width="160"/>

            <template v-for="(col, key, index) in cols">
              <el-table-column
                :prop="col.prop"
                :label="col.label"
                :width="col.width || 90"
                v-bind:key="index"
              >
                <el-table-column label="个数" sortable :prop="col.actualNum" width="80"/>
                <el-table-column label="总数" :prop="col.expectedNum" width="80"/>
                <el-table-column label="完整率" sortable :prop="col.result" width="80">
                  <template slot-scope="scope">
                    <span>{{scope.row[col.result]*100 | rounding}}%</span>
                  </template>
                </el-table-column>
                <!--
              <template slot-scope="scope">
                <el-tag type="primary" v-if="col.type==='sort'">{{ scope.row.type }}</el-tag>
                <i :class="{'el-icon-check': 'currentDataIsUpload' == col.prop && scope.row.firstDataQuarter && scope.row.firstDataQuarter == scope.row.currentDataQuarter}"></i>
                <span>{{ scope.row[col.prop] }}</span>
              </template>
                -->
              </el-table-column>
            </template>
          </el-table>
        </div>
        <!--
        <div class="paginationClass">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
        -->
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
.data-body {
  padding: 0;
}

.pane-head-label {
  line-height: 30px;
}

.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

.upload-box {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__input {
  display: none !important;
}
.form-row {
  margin: 4px 0;
}
.form-h-item .el-form-item__label {
  width: 5.2em;
}
.form-h-item .el-form-item__content {
  width: 160px;
}
.stat-bar {
  margin-left: 16px;
}
.stat-bar strong {
  margin-right: 8px;
}
</style>

<script>
import global from "@/config/global-config";
import orgTree from "@/config/orgTree-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import StringUtil from "@/utils/StringUtil";

import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_indexIntegrityRate",
  
  data() {
    return {
      user: {
        roles: [],
        canStat: false,
        canEdit: false
      },
      total: 0,
      currentPage: 1,
      pageSize: 20,
      loading: false,

      tableDataAction:
        //"http://127.0.0.1:8880/rh-cmis"+ "/carePlatform/index/IndexIntegrirtyRate",
        global.cmis.domain + "/carePlatform/index/IndexIntegrirtyRate",

      tabs: {
        onQuarter: "primary",
        onYear: ""
      },

      form: {
        visible: true,

        props: { value: "value" },
        combo_fromYear: formConfig.combo_year,
        combo_fromQuarter: formConfig.combo_quarter,
        combo_toYear: formConfig.combo_year,
        combo_toQuarter: formConfig.combo_quarter,
        combo_area: formConfig.combo_areaHierarchical,
        comob_provice: formConfig.comob_provice,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        //combo_orgPartId: formConfig.combo_orgPartId,
        // combo_orgPartId: (function() {
        //   var tmp = formConfig.combo_orgPartId;
        //   if (tmp[0].label == "所有") tmp.shift();
        //   return tmp;
        // })(),
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgGrade: formConfig.combo_orgGrade,

        // value_fromYear: "",
        // value_fromQuarter: "",
        // value_toYear: "",
        // value_toQuarter: "",
        // 默认6个月前
        value_fromYear:
          new Date(
            DateUtil.dateAdd("q", -5, new Date())
          ).getFullYear() + "",
        value_fromQuarter:
          parseInt(
            new Date(
              DateUtil.dateAdd("q", -5, new Date())
            ).getMonth() / 3
          ) +
          1 +
          "",

        value_toYear: DateUtil.dateAdd("q", -1, new Date()).getFullYear() + "",
        value_toQuarter:
          parseInt(
            DateUtil.dateAdd("q", -1, new Date()).getMonth() /
              3
          ) +
          1 +
          "",

        area_cfg: (function() {
          var tmp = formConfig.combo_area;
          return tmp.sort(function(a, b) {
            return a.value - b.value;
          });
        })(),
        value_area: formConfig.value_user.org_area,
        value_province: "",
        value_city: "",
        value_orgPartLabel: "全院",
        value_orgPartId: ["1"], //缺省全院
        value_nationalUpload: "",
        value_checkState: "2",
        value_orgGrade: "",
        value_orgGraStr: "所有"
      },
      cols: [],
      tableData: []
    };
  },

  mounted: function() {
    var self = this;
    /*
    var idx = -1;
    idx = formConfig.getForm(
      self.form.area_cfg,
      formConfig.value_user.org_area[0]
    );
    if (idx != -1) self.form.value_province = self.form.area_cfg[idx].label;

    idx = -1;
    idx = formConfig.getForm(
      self.form.area_cfg,
      formConfig.value_user.org_area[1]
    );

    if (idx != -1) self.form.value_city = self.form.area_cfg[idx].label;
    */
    self.getTableData();

    self.setupUserPermission();
  },
  filters: {
    rounding(value) {
      if (typeof value == "number") {
        return value.toFixed(3);
      } else {
        return value;
      }
    }
  },

  methods: {
    handleRowClick(row, event, column) {
      var self = this;
      //timeStr 2019年1季度
      var regExp = /[0-9]+/g;
      var timeObj = row.timeStr.match(regExp);
      if (timeObj == null || (timeObj.length != 2 && timeObj.length != 4)) {
        self.$alert("时间[" + row.timeStr + "]不正确!");
        return;
      }

      var toDate, toStr;
      var fromDate = new Date(timeObj[0], (timeObj[1] - 1) * 3, 1);
      var fromStr = DateUtil.dateToStr(fromDate, "yyyy-MM-dd");
      if (timeObj.length == 2) {
        toStr = fromStr;
      } else {
        toDate = new Date(timeObj[2], (timeObj[3] - 1) * 3, 1);
        toStr = DateUtil.dateToStr(toDate, "yyyy-MM-dd");
      }

      //var prov = this.getCity(this.form.combo_area, row.provinceName);
      //var city = this.getCity(this.form.combo_area, row.areaName);
      if (null != row.areaName) {
        var city = StringUtil.getIdsByName(
          formConfig.value_areaId,
          formConfig.value_areaName,
          row.areaName
        );
      } else {
        var city = StringUtil.getIdsByName(
          formConfig.value_areaId,
          formConfig.value_areaName,
          row.provinceName
        );
      }
      // var areas = "";
      // if (city != null) {
      //   if (city.length >= 2) {
      //     areas = city[0] + "," + city[1];
      //   } else areas = city[0];
      // }
      // if (prov != null) areas += prov;
      // if (city != null) areas += "," + city;

      this.$router.push({
        name: "指标数据查询",
        params: {
          fromTime: fromStr,
          toTime: toStr,
          orgPartId: this.form.value_orgPartId + "",
          area: city
        },
        query: {}
      });
      //query:{orgPartId:"1",area:'2080'}});
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    getTableData: function() {
      var self = this;

      self.form.value_orgPartLabel = (function() {
        for (var i = 0; i < self.form.combo_orgPartId.length; i++) {
          if (self.form.combo_orgPartId[i].value == self.form.value_orgPartId)
            return self.form.combo_orgPartId[i].label;
        }
        return self.form.value_orgPartLabel;
      })();

      self.form.value_orgGraStr = (function() {
        for (var i = 0; i < self.form.combo_orgGrade.length; i++) {
          if (self.form.combo_orgGrade[i].value == self.form.value_orgGrade)
            return self.form.combo_orgGrade[i].label;
        }
        return self.form.value_orgGrade;
      })();
      self.loading = true;
      self.tableData = [];
      var lastNode = StringUtil.getLastNode(self.form.value_area);
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.tableDataAction,
        data: {
          //pageNo: self.currentPage,
          //pageSize: self.pageSize,

          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_toYear,
          toQuarter: self.form.value_toQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          orgPart: (function() {
            var id = StringUtil.getLastNode(self.form.value_orgPartId);
            if (id == "") return "";
            if (id in formConfig.value_orgPartId) {
              return formConfig.value_orgPartId[id].dictLabel;
            } else return "";
          })(),
          nationUploadId: self.form.value_nationalUpload,
          checkStateId: self.form.value_checkState,
          checkCfgId: 3,
          orgGrade: self.form.value_orgGrade,
          orgGraStr: self.form.value_orgGraStr,
          //province: self.form.value_province,
          //citys: self.form.value_city,
          //" "空格当作一个特例主要做后面List转换
          citys:StringUtil.getTreeNames(formConfig.value_areaId,lastNode),
          periodId: 4 //季度 4 年度 5
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.total = res.count;
          self.getIndexItems(res.list);
          self.getTableDatas(res.list);
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    getTableDatas: function(data) {
      var self = this;
      if (0 >= self.total) {
        return;
      }
      var dataList = data ? data : null;
      if (!dataList || !dataList.length) {
        return;
      }
      for (var i = 0; i < dataList.length; i++) {
        var itemList = dataList[i];
        var dataRec = {};
        for (var j = 0; j < itemList.length; j++) {
          if (j == 0) {
            for (var prop in itemList[j]) {
              dataRec[prop] = itemList[j][prop];
            }
          } else {
            var idx = itemList[j]["idxId"];
            for (var prop in itemList[j])
              dataRec[prop + idx] = itemList[j][prop];
          }
          var item = itemList[j];
        }
        self.tableData.push(dataRec);
      }
    },

    getIndexItems: function(data) {
      var self = this;
      //是否有单位档案
      if (0 >= self.total) {
        return;
      }

      var colDocNum = 0;
      var idxItems = data ? data[0] : null;
      if (!idxItems || !idxItems.length) {
        return;
      }

      for (var j = 1, itm, prop, iprop; j < idxItems.length; j++) {
        itm = idxItems[j];
        //
        prop = itm["idxName"]; //itm["id"];
        iprop = false; //是否有了这个指标列
        for (var j1 = colDocNum; j1 < self.cols.length; j1++) {
          if ((iprop = prop == self.cols[j1]["idxName"])) {
            break;
          }
        }
        if (!iprop) {
          self.cols.push({
            label: itm["idxName"],
            prop: prop,
            type: "normal",
            actualNum: "actualNum" + itm["idxId"],
            expectedNum: "expectedNum" + itm["idxId"],
            result: "value" + itm["idxId"]
          });
        }
        //
      }

      //
      //
    },

    onSearch: function() {
      this.getTableData();
    },

    onChangeOrg: function(data) {
      this.getTableData();
    },

    /**
     * 用户权限: 管理员|单位人员
     */
    setupUserPermission: function() {
      var self = this;

      var userInfo = LoginHelper.logincheck(),
        usercode = userInfo ? userInfo.usercode : null;
      //权限, 临时
      //self.user.canStat = usercode && /^(system|bak[0-9]+)$/.test(usercode);
      //self.user.canEdit = usercode && /^(cdr[0-9]+)$/.test(usercode);
      var ajaxOpts = {
        async: false,
        url: global.cmis.domain + "/carePlatform/index/checkRights",
        type: "post",
        dataType: "json",
        data: { code: usercode },
        success: function(res) {
          if (res) {
            self.user.canStat = !!res.canStat;
            self.user.canEdit = !!res.canEdit;
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    }
  }
};
</script>

