<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div>
          <el-row class="pane-head">
            <el-col :span="6" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;上报质量分析
            </el-col>
            <el-col :span="8">
              <el-button size="mini" class="tab-button" :type="tabs.onQuarter">按季度</el-button>
              <el-button size="mini" class="tab-button" :type="tabs.onYear">按年度</el-button>
            </el-col>
            <el-col :span="10">
              <el-button size="mini">导出</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="地区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="开始年份" class="form-h-item">
                  <el-select v-model="form.value_fromYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="截止年份" class="form-h-item">
                  <el-select v-model="form.value_toYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度" class="form-h-item">
                  <el-select v-model="form.value_toQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="上报国家" class="form-h-item">
                  <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位定级" class="form-h-item">
                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="审核状态" class="form-h-item">
                  <el-select v-model="form.value_checkState" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_checkState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content pb0 viewWrapper">
          <!-- Chart boxes -->
          <div class="row">
            <div class="col-md-12">
              <div class="box box-widget">
                <div class="box-header with-border">
                  <h3 class="box-title">按时间数据质量分析</h3>
                  <div class="box-tools pull-right">
                    <div class="btn-group">
                      <button
                        type="button"
                        class="btn btn-box-tool dropdown-toggle"
                        data-toggle="dropdown"
                      >
                        <i class="fa fa-bars"></i>
                      </button>
                      <ul class="dropdown-menu" role="menu">
                        <li>
                          <a href="#">导出Excel</a>
                        </li>
                        <li>
                          <a href="#">导出Word</a>
                        </li>
                        <li class="divider"></li>
                        <li>
                          <a href="#">基本设置</a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="box-body">
                  <div class="row">
                    <div>
                      <div class="chart">
                        <div id="statCheckState" style="height:300px;"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-md-9">
                  <div class="box box-widget">
                    <div class="box-header with-border">
                      <h3 class="box-title">{{form.value2_fromYearQuarter}}地市数据质量</h3>
                      <div class="box-tools pull-right">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-box-tool dropdown-toggle"
                            data-toggle="dropdown"
                          >
                            <i class="fa fa-bars"></i>
                          </button>
                          <ul class="dropdown-menu" role="menu">
                            <li>
                              <a href="#">导出Excel</a>
                            </li>
                            <li>
                              <a href="#">导出Word</a>
                            </li>
                            <li class="divider"></li>
                            <li>
                              <a href="#">基本设置</a>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="box-body">
                      <div class="row">
                        <div>
                          <div class="chart">
                            <div id="statCheckStateCity" style="height:300px;"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="box box-widget">
                    <div class="box-header with-border">
                      <h3 class="box-title">{{form.value2_fromYearQuarter}}分级分析</h3>
                      <div class="box-tools pull-right">
                        <div class="btn-group">
                          <button
                            type="button"
                            class="btn btn-box-tool dropdown-toggle"
                            data-toggle="dropdown"
                          >
                            <i class="fa fa-bars"></i>
                          </button>
                          <ul class="dropdown-menu" role="menu">
                            <li>
                              <a href="#">导出Excel</a>
                            </li>
                            <li>
                              <a href="#">导出Word</a>
                            </li>
                            <li class="divider"></li>
                            <li>
                              <a href="#">基本设置</a>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div class="box-body">
                      <div class="row">
                        <div>
                          <div class="chart">
                            <div id="statCheckStateGrade" style="height:300px;"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--
        <div class="paginationClass">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
        -->
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
.data-body {
  padding: 0;
}

.pane-head-label {
  line-height: 30px;
}

.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

.upload-box {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__input {
  display: none !important;
}
.form-row {
  margin: 4px 0;
}
.form-h-item .el-form-item__label {
  width: 5.2em;
}
.form-h-item .el-form-item__content {
  width: 160px;
}
.stat-bar {
  margin-left: 16px;
}
.stat-bar strong {
  margin-right: 8px;
}
.dataTableContainer {
  margin: 8px auto;
}
/* 图形 */
div.viewWrapper {
  padding: 2px;
}
.viewWrapper .row {
  margin-left: 0;
  margin-right: 0;
}
.viewWrapper .box-widget {
  margin-bottom: 2px;
}
.viewWrapper .box-header .box-title {
  display: block;
  margin: 0 0px;
  text-align: center;
}
.viewWrapper .box-body {
  padding: 1px;
}

.viewWrapper .col-md-12,
.viewWrapper .col-md-5,
.viewWrapper .col-md-7 {
  padding-right: 2px;
  padding-left: 2px;
}

.viewDataSheet .ui-widget-content {
  border-width: 0;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-hdiv {
  border-color: #949397;
}
.viewDataSheet .ui-jqgrid-htable thead tr,
.viewDataSheet .ui-jqgrid-hdiv,
.viewDataSheet .ui-jqgrid-hbox {
  background-color: #6b7984;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-labels th {
  border-color: #949397 !important;
}
.viewDataSheet .ui-jqgrid-sortable {
  color: #f9fefe;
}

.viewDataSheet .ui-jqgrid-bdiv .ui-priority-secondary {
  background-color: #d2e4e6;
}
.fa-red {
  color: red;
}
.fa-green {
  color: green;
}
</style>

<script>
import global from "@/config/global-config";
import orgTree from "@/config/orgTree-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import StringUtil from "@/utils/StringUtil";

import formConfig from "@/config/uploadLog-config";
import echarts from "echarts";

export default {
  name: "page_reportStatCheckState",
  
  data() {
    return {
      user: {
        roles: [],
        canStat: false,
        canEdit: false
      },
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      cityTableData: [],
      gradeTableData: [],
      loading: false,
      ChartsDataAction:
        global.cmis.domain + "/carePlatform/index/statcheckstate",
      cityChartsDataAction:
        global.cmis.domain + "/carePlatform/index/statcheckstatecity",
      gradeChartsDataAction:
        global.cmis.domain + "/carePlatform/index/statcheckstategrade",
      tabs: {
        onQuarter: "primary",
        onYear: ""
      },
      form: {
        visible: true,

        combo_fromYear: formConfig.combo_year,
        combo_fromQuarter: formConfig.combo_quarter,
        combo_toYear: formConfig.combo_year,
        combo_toQuarter: formConfig.combo_quarter,
        combo_area: formConfig.combo_areaHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgGrade: formConfig.combo_orgGrade,
        // 默认6个月前
        value_fromYear:
          new Date(
            new Date().setDate(new Date().getDate() - 540)
          ).getFullYear() + "",
        value_fromQuarter:
          parseInt(
            new Date(
              new Date().setDate(new Date().getDate() - 540)
            ).getMonth() / 3
          ) +
          1 +
          "",

        value_toYear: new Date().getFullYear() + "",
        value_toQuarter:
          parseInt(
            new Date(new Date().setDate(new Date().getDate() - 90)).getMonth() /
              3
          ) +
          1 +
          "",
        value2_fromYearQuarter:
          new Date(
            new Date().setDate(new Date().getDate() - 540)
          ).getFullYear() +
          "年第" +
          parseInt(
            new Date(
              new Date().setDate(new Date().getDate() - 540)
            ).getMonth() /
              3 +
              1
          ) +
          "季度",
        value2_fromYear:
          new Date(
            new Date().setDate(new Date().getDate() - 540)
          ).getFullYear() + "", //value_fromYear,
        value2_fromQuarter:
          parseInt(
            new Date(
              new Date().setDate(new Date().getDate() - 540)
            ).getMonth() / 3
          ) +
          1 +
          "", //value_fromQuarter,
        value_area: formConfig.value_user.org_area, //["2080"],
        value_orgPartId: ["1"],
        value_nationalUpload: "",
        value_checkState: "",
        value_orgGrade: ""
      },
      // 基于准备好的dom，初始化echarts实例
      statCheckState: null,
      statCheckStateCity: null,
      statCheckStateGrade: null
    };
  },

  mounted: function() {
    var self = this;

    self.getTableData();

    self.setupUserPermission();
  },

  methods: {
    handleRowClick(row, event, column) {},

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },
    //请求季度统计值
    getTableData: function() {
      var self = this;
      //self.$alert("getTableData");
      self.loading = true;
      //地区选择
      var value_area = self.form.value_area;
      value_area =
        value_area && value_area.length
          ? value_area[value_area.length - 1]
          : "";
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.ChartsDataAction,
        data: {
          //pageNo: self.currentPage,
          //pageSize: self.pageSize,
          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_toYear,
          toQuarter: self.form.value_toQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "organization.extend.extendF1": self.form.value_nationalUpload,
          check: self.form.value_checkState,
          "organization.extend.extendS1": self.form.value_orgGrade,
          "organization.extend.extendS5": value_area,
          dataPeriod: "4"
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.tableData = res.data;
          self.total = res.count;
          self.drawChart();
          //联动刷新 地区 等级 统计表
          self.form.value2_fromYearQuarter =
            self.form.value_fromYear +
            "年第" +
            self.form.value_fromQuarter +
            "季度";
          self.form.value2_fromYear = self.form.value_fromYear;
          self.form.value2_fromQuarter = self.form.value_fromQuarter;
          self.form.value2_toYear = self.form.value_fromYear;
          self.form.value2_toQuarter = self.form.value_fromQuarter;
          self.getCityTableData();
          self.getGradeTableData();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    //绘制季度统计值
    drawChart() {
      var self = this;
      //  首次绘制图表
      if (self.statCheckState == null) {
        //self.$alert("drawChart 1");
        self.statCheckState = echarts.init(
          document.getElementById("statCheckState")
        );
      }
      self.statCheckState.setOption(
        {
          legend: { y: "bottom" },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            }
          },
          grid: {
            top: "6%", //距离上边的距离
            bottom: "12%",
            left: "3%",
            right: "3%",
            containLabel: true
          },
          dataset: {
            source: self.tableData.listListEchartsData
            /* source: [
            ["数据状态", "通过", "上报未通过", "未上报"],
            ["2019年第1季度", 43, 85, 10],
            ["2019年第2季度", 83, 73, 10]
          ]*/
          },
          xAxis: { type: "category" },
          yAxis: {},
          // Declare several bar series, each will be mapped
          // to a column of dataset.source by default.
          series: self.tableData.listEchartsSeries // [{ type: "bar" }, { type: "bar" }, { type: "bar" }]
        },
        true
      );
      self.statCheckState.on("click", function(params) {
        var pFromYear = params.name.substring(0, 4);
        var pFromQuarter = params.name.substring(6, 7);
        //self.$alert(pFromYear + "==" + pFromQuarter);
        self.form.value2_fromYearQuarter = params.name;
        self.form.value2_fromYear = pFromYear;
        self.form.value2_fromQuarter = pFromQuarter;
        self.form.value2_toYear = pFromYear;
        self.form.value2_toQuarter = pFromQuarter;
        self.getCityTableData();
        self.getGradeTableData();
      });
    },
    //请求地区统计值
    getCityTableData: function() {
      var self = this;
      /*self.$alert(
        "getCityTableData:" +
          self.form.value2_fromYear +
          "==" +
          self.form.value2_fromQuarter
      );*/

      self.loading = true;
      //地区选择
      var value_area = self.form.value_area;
      value_area =
        value_area && value_area.length
          ? value_area[value_area.length - 1]
          : "";
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.cityChartsDataAction,
        data: {
          //pageNo: self.currentPage,
          //pageSize: self.pageSize,
          fromYear: self.form.value2_fromYear,
          fromQuarter: self.form.value2_fromQuarter,
          toYear: self.form.value2_fromYear,
          toQuarter: self.form.value2_fromQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "organization.extend.extendF1": self.form.value_nationalUpload,
          check: self.form.value_checkState,
          "organization.extend.extendS1": self.form.value_orgGrade,
          "organization.extend.extendS5": value_area,
          dataPeriod: "4"
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.cityTableData = res.data;
          self.total = res.count;
          self.drawCityChart();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    //绘制地区统计值
    drawCityChart() {
      var self = this;
      //self.$alert("drawCityChart");
      //console.log("drawCityChart 1" + self.cityTableData.listListEchartsData);
      //console.log("drawCityChart 1" + self.cityTableData.listEchartsSeries);
      // 基于准备好的dom，初始化echarts实例
      if (self.statCheckStateCity == null) {
        self.statCheckStateCity = echarts.init(
          document.getElementById("statCheckStateCity")
        );
      }
      // 绘制图表
      self.statCheckStateCity.setOption(
        {
          legend: { y: "bottom" },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            }
          },
          grid: {
            top: "6%", //距离上边的距离
            bottom: "12%",
            left: "3%",
            right: "3%",
            containLabel: true
          },
          dataset: {
            source: self.cityTableData.listListEchartsData
            /*source: [
            ["checkState", "通过", "上报未通过", "未上报"],
            ["区直", 43.3, 85.8, 93.7],
            ["南宁", 83.1, 73.4, 55.1],
            ["柳州", 86.4, 65.2, 82.5],
            ["桂林", 72.4, 53.9, 39.1],
            ["梧州", 43.3, 85.8, 93.7],
            ["北海", 83.1, 73.4, 55.1],
            ["防城港", 86.4, 65.2, 82.5],
            ["钦州", 72.4, 53.9, 39.1],
            ["贵港", 43.3, 85.8, 93.7],
            ["玉林", 83.1, 73.4, 55.1],
            ["百色", 86.4, 65.2, 82.5],
            ["贺州", 72.4, 53.9, 39.1],
            ["贺州", 43.3, 85.8, 93.7],
            ["来宾", 83.1, 73.4, 55.1],
            ["崇左", 86.4, 65.2, 82.5]
          ]*/
          },
          xAxis: { type: "category" },
          yAxis: {},
          // Declare several bar series, each will be mapped
          // to a column of dataset.source by default.
          series: self.cityTableData.listEchartsSeries
        },
        true
      );
    },
    //请求分级统计值
    getGradeTableData: function() {
      var self = this;
      /*self.$alert(
        "getGradeTableData:" +
          self.form.value2_fromYear +
          "==" +
          self.form.value2_fromQuarter
      );*/

      self.loading = true;
      //地区选择
      var value_area = self.form.value_area;
      value_area =
        value_area && value_area.length
          ? value_area[value_area.length - 1]
          : "";
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.gradeChartsDataAction,
        data: {
          //pageNo: self.currentPage,
          //pageSize: self.pageSize,
          fromYear: self.form.value2_fromYear,
          fromQuarter: self.form.value2_fromQuarter,
          toYear: self.form.value2_fromYear,
          toQuarter: self.form.value2_fromQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "organization.extend.extendF1": self.form.value_nationalUpload,
          check: self.form.value_checkState,
          "organization.extend.extendS1": self.form.value_orgGrade,
          "organization.extend.extendS5": value_area,
          dataPeriod: "4"
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.gradeTableData = res.data;
          self.total = res.count;
          self.drawGradeChart();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    //绘制分级图表
    drawGradeChart() {
      var self = this;
      //self.$alert("drawGradeChart");
      //console.log("drawGradeChart 1" + self.gradeTableData.listListEchartsData);
      //console.log("drawGradeChart 1" + self.gradeTableData.listEchartsSeries);
      // 基于准备好的dom，初始化echarts实例
      if (self.statCheckStateGrade == null) {
        self.statCheckStateGrade = echarts.init(
          document.getElementById("statCheckStateGrade")
        );
      }
      // 绘制图表
      self.statCheckStateGrade.setOption(
        {
          legend: { y: "bottom" },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            }
          },
          grid: {
            top: "6%", //距离上边的距离
            bottom: "12%",
            left: "3%",
            right: "3%",
            containLabel: true
          },
          dataset: {
            source: self.gradeTableData.listListEchartsData
            /* source: [
            ["checkState", "通过", "未上报"],
            ["二级", 43, 85],
            ["三级", 83, 73]
          ]*/
          },
          xAxis: { type: "category" },
          yAxis: {},
          // Declare several bar series, each will be mapped
          // to a column of dataset.source by default.
          series: self.gradeTableData.listEchartsSeries
        },
        true
      );
    },
    onSearch: function() {
      this.getTableData();
    },

    onChangeOrg: function(data) {
      this.getTableData();
    },

    /**
     * 用户权限: 管理员|单位人员
     */
    setupUserPermission: function() {
      var self = this;

      var userInfo = LoginHelper.logincheck(),
        usercode = userInfo ? userInfo.usercode : null;
      //权限, 临时
      //self.user.canStat = usercode && /^(system|bak[0-9]+)$/.test(usercode);
      //self.user.canEdit = usercode && /^(cdr[0-9]+)$/.test(usercode);
      var ajaxOpts = {
        async: false,
        url: global.cmis.domain + "/carePlatform/index/checkRights",
        type: "post",
        dataType: "json",
        data: { code: usercode },
        success: function(res) {
          if (res) {
            self.user.canStat = !!res.canStat;
            self.user.canEdit = !!res.canEdit;
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    }
  }
};
</script>

