<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div>
          <el-row class="pane-head">
            <el-col :span="6" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;数据填报
            </el-col>
<!--            <el-col :span="6">
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.dataEdit"
                @click="pushDataEdit"
              >数据填报
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.indexComparison"
                @click="pushIndexComparison"
              >指标预览
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.dataComparison"
                @click="pushDataComparison"
              >上季度数据对比
              </el-button>
            </el-col>-->
            <el-col :span="6">
              <div class="step-bar-pane" style="margin-top:6px;height:20px;">
                <div class="mini-step-bar" v-if="!!form.checkFlow && !!form.checkFlow.lastNodeId">
                  <el-steps simple :active="flowStepActive(form.checkFlow)" finish-status="success">
                    <el-step title="提交"></el-step>
                    <!-- <el-step title="院内审核" :status="flowStepStatus(2, form.checkFlow)"></el-step>
                    <el-step title="省级审核" :status="flowStepStatus(3, form.checkFlow)"></el-step>-->
                    <el-step
                      title="院内审核"
                      :status="flowStepStatus(2, form.checkFlow)"
                      v-if="!!form.checkFlow && form.checkFlow.endNodeId>=2"
                    ></el-step>
                    <el-step
                      title="省级审核"
                      :status="flowStepStatus(3, form.checkFlow)"
                      v-if="!!form.checkFlow && form.checkFlow.endNodeId>=3"
                    ></el-step>
                  </el-steps>
                </div>
              </div>
            </el-col>
<!--            <el-col :span="6">
              <el-button size="mini" @click="downLoad(form.value_reportTemplate)">下载模板</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{ form.visible ? '隐藏' : '展开' }}查询条件
              </el-button>
            </el-col>-->
          </el-row>
        </div>
        <div>
          <keep-alive>
            <component
              :is="view"
              :fatherData="showChildPaneHead"
              ref="ViewComponent"
              @setCheckFlow="setCheckFlow"
              @setTemplate="setTemplate"
            ></component>
          </keep-alive>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
</style>

<script>
import global from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";

import IndexItems from "@/components/pages/indexItems";
import QcIndexSourceView from "@/components/pages/qcIndexSourceView";
import QcIndexView from "@/components/pages/qcIndexView";

export default {
  name: "page_indexItemsPanels",

  components: {
    vDataEdit: IndexItems,
    vDataComparison: QcIndexSourceView,
    vIndexComparison: QcIndexView
  },
  data() {
    return {
      view: null,
      showChildPaneHead: false,

      tabs: {
        dataEdit: "primary",
        indexComparison: "",
        dataComparison: ""
      },
      form: {
        visible: true,
        //流程
        checkFlow: null,
        value_reportTemplate: 1
      },
      approveForm: {
        enableApprove: true
      }
    };
  },
  created: function () {
    var self = this;
    //把开始时间设置为上一季度，以便上季度对比页面使用
    if (null != self.$route.params && null != self.$route.params["fromTime"]) {
      // var dateFromTime = DateUtil.strFormatToDate(
      //   self.$route.params["fromTime"],
      //   "yyyy-MM-dd"
      // );
      var dateFromTime = self.$route.params["fromTime"];
      //取上一季度
      var lastFromTime = DateUtil.dateAdd("q", -1, dateFromTime);
      // DateUtil.dateToStr(
      //   DateUtil.quarterHeadDate(
      //     //new Date(dateFromTime.setDate(dateFromTime.getDate() - 90))
      //     DateUtil.dateAdd("q", -1, dateFromTime)
      //   ),
      //   "yyyy-MM-dd"
      // );
      self.$route.params["fromTime"] = lastFromTime;
    }

    self.view = "vDataEdit";
  },
  methods: {
    downLoad(template) {
      if (template == null) window.open("static/template/填报模板.rar");

      if (template == 2)
        //病区
        window.open(
          "static/template/附件5：2019第1季度病区填报模板(xx病区).xlsx"
        );
      else if (template == 3)
        window.open("static/template/附件6：2019年第1季度ICU填报模板.xlsx");
      else window.open("static/template/填报模板.rar");
    },

    swapFormVisibility: function () {
      this.form.visible = !this.form.visible;
    },

    pushDataEdit() {
      var self = this;
      self.view = "vDataEdit";

      self.tabs.dataEdit = "primary";
      self.tabs.indexComparison = "";
      self.tabs.dataComparison = "";
    },
    pushIndexComparison() {
      var self = this;
      self.view = "vIndexComparison";

      self.tabs.dataEdit = "";
      self.tabs.indexComparison = "primary";
      self.tabs.dataComparison = "";
    },
    pushDataComparison() {
      var self = this;
      self.view = "vDataComparison";

      self.tabs.dataEdit = "";
      self.tabs.indexComparison = "";
      self.tabs.dataComparison = "primary";
    },
    /**
     * 流程节点状态, 不通过标红
     */
    flowStepStatus: function (step, flow) {
      return !!flow && step == flow.lastNodeId && "4" == flow.lastCheckState
        ? "error"
        : "";
    },
    /**
     * 当前流程节点
     */
    flowStepActive: function (flow) {
      var lastNodeId = !!flow && flow.lastNodeId;
      if (!lastNodeId) {
        return -1;
      }
      //返修不通过, 节点回退
      if (lastNodeId > 1 && "3" == flow.lastCheckState) {
        return 0;
      }

      return lastNodeId;
    },

    setCheckFlow: function (flow) {
      this.form.checkFlow = flow;
    },
    setTemplate: function (template) {
      this.form.value_reportTemplate = template;
    }
  }
};
</script>
