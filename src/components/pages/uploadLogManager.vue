<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="2" :md="2" class="pane-head-label">
              <i class="el-icon-menu"></i>
              任务管理
            </el-col>
<!--            <el-col :span="10">
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.reportTask"
                @click="pushReport"
              >敏感指标({{ stat.numJobType1 }})
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.infoEditTask"
                @click="pushInfoEdit"
                v-if="tabs.isManager"
              >档案修改({{ stat.numJobType3 }})
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.reportTask4"
                @click="pushReport4"
              >不良事件({{ stat.numJobType4 }})
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.reportTask5"
                @click="pushReport5"
              >优质护理({{ stat.numJobType5 }})
              </el-button>
            </el-col>-->
            <el-col :span="10" :md="10">
              <div class="stat-bar tab-button" v-if="stat.enable">
                <strong>今日提交病区：{{ stat.numCommittedToday }}</strong>
                <strong>上季度已提交病区：{{ stat.numCommittedPrequarter }}/{{ stat.numTocommitPrequarter }}</strong>
                <strong
                  style="color:red;"
                >上季度未提交病区：{{
                    stat.numTocommitPrequarter - stat.numCommittedPrequarter
                  }}/{{ stat.numTocommitPrequarter }}</strong>
              </div>
              <span v-if="!stat.enable">&nbsp;</span>
            </el-col>
            <el-col :span="2">
              <!-- <el-upload
			  class="upload-box"
			  :action="uploadAction"
			  multiple
			  :limit="1"
			  :show-file-list="false"
			  :on-success="handleSuccessUpload"
			  :on-error="handleErrorUpload"
			  :on-progress="handleProgressUpload">
			  <el-button size="mini" type="primary">上传</el-button>
			</el-upload>
              <el-button size="mini">下载模板</el-button>-->
<!--              <el-button size="mini" @click="swapFormVisibility">{{ form.visible ? '隐藏' : '展开' }}查询条件
              </el-button>-->
            </el-col>
          </el-row>
        </div>
        <div
          id="searchFormPane"
          class="search-form-pane"
          :style="{display: form.visible? '' : 'none'}"
        >
          <el-form :inline="true" class="search-form-inline" size="mini">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="地区" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="单位名称" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-input v-model="form.value_orgName"></el-input>
                </el-form-item>

                <!--
                <el-form-item label="科室病区" class="form-h-item">
                  <el-select v-model="form.value_orgPartId" placeholder="全部">
                    <el-option
                      v-for="item in form.combo_orgPartId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                -->
                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>
                <!--
                  <el-form-item label="单位" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgId"
                    placeholder="试试搜索"

                    :options="form.combo_orgId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>
                -->
                <el-form-item label="上报模板" class="form-h-item">
                  <el-select v-model="form.value_reportTemplate" placeholder="全部">
                    <el-option
                      v-for="item in form.combo_reportTemplate"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="上报国家" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_nationalUpload" placeholder>
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="提交状态" class="form-h-item">
                  <el-select v-model="form.value_submitState" placeholder>
                    <el-option
                      v-for="item in form.combo_submitState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
<!--                <el-form-item label="终审状态" class="form-h-item">
                  <el-select v-model="form.value_checkState" placeholder="全部">
                    <el-option
                      v-for="item in form.combo_checkState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->

                <el-form-item label="单位定级" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_orgGrade" placeholder>
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位定等" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_orgClass" placeholder>
                    <el-option
                      v-for="item in form.combo_orgClass"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位类型" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_medinstCategory" placeholder>
                    <el-option
                      v-for="item in form.combo_medinstCategory"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位性质" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_orgProp" placeholder>
                    <el-option
                      v-for="item in form.combo_orgProp"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="是否教学" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_edu" placeholder>
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="隶属类型" class="form-h-item" v-if="form.filterLevel == 0">
                  <el-select v-model="form.value_orgAttachType" placeholder>
                    <el-option
                      v-for="item in form.combo_orgAttachType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="年份" class="form-h-item">
                  <el-select v-model="form.value_year" placeholder>
                    <el-option
                      v-for="item in form.combo_year"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="季度" class="form-h-item">
                  <el-select v-model="form.value_quarter" placeholder>
                    <el-option
                      v-for="item in form.combo_quarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->
                <el-form-item label="任务时间" class="form-h-item" v-if="form.value_jobPeriod == '4'">
                  <el-select v-model="form.value_fromTime" clearable placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="任务时间" v-if="form.value_jobPeriod != '4'">
                  <el-date-picker
                    type="monthrange"
                    v-model="form.value_searchDataTime"
                    range-separator="——"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    align="right"
                    unlink-panels
                    :picker-options="pickerOptions"
                    size="mini"
                    format="yyyy 年 MM 月"
                  ></el-date-picker>
                </el-form-item>
                <!-- <el-form-item label="任务分类" class="form-h-item">
                  <el-select v-model="form.value_jobType" placeholder="全部">
                    <el-option
                      v-for="item in form.combo_jobType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
<!--                  <el-button type="primary" @click="saveData">新增</el-button>-->
                  <el-button type="primary" @click="getData">刷新</el-button>
                  <el-button>重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <div class="dataTableContainer">
            <el-table
              :data="tableData"
              class="tb-edit"
              style="width: 100%"
              highlight-current-row
              stripe
              border
              size="mini"
              v-loading="loading"
              :height="dataTableHeight"
            >
              <el-table-column type="index" width="50"/>
              <el-table-column label="单位名称" prop="orgName" width="240"/>
              <!-- <el-table-column label="任务分类" prop="report.jobTypeName" width="160"/> -->
              <el-table-column
                v-if="tabs.infoEditTask==''"
                label="科室病区"
                prop="orgPartName"
                width="160"
              />
              <el-table-column
                v-if="tabs.infoEditTask==''"
                label="数据时间"
                prop="fromQuarterName"
                width="160"
              />
<!--              <el-table-column label="审核流程">
                <template slot-scope="scope">
                  <div class="mini-step-bar">
                    <el-steps simple :active="scope.row.lastCheckNodeId" finish-status="success">
                      <el-step title="提交"></el-step>
                      <el-step
                        title="院内审核"
                        :status="flowStepStatus(2, scope.row)"
                        v-if="scope.row.endNodeId>=2"
                      ></el-step>
                      <el-step
                        title="省级审核"
                        :status="flowStepStatus(3, scope.row)"
                        v-if="scope.row.endNodeId>=3"
                      ></el-step>
                    </el-steps>
                  </div>
                </template>
              </el-table-column>-->
              <!-- <el-table-column label="提交状态" prop="execStatusName" width="96"/> -->
<!--              <el-table-column label="终审状态" prop="finalCheckName" width="96"/>-->
              <!-- <el-table-column label="最近审核人" prop="lastCheckUserName" width="96"/> -->
              <!-- <el-table-column label="最近审核状态" prop="lastCheckName" width="96" />
              <el-table-column label="审核意见" prop="lastCheckFeedback" />-->
              <el-table-column label="操作" width="220">
                <template slot-scope="scope">
                  <!-- <a href="javascript://" @click="handleEdit('view', scope.row)">查看数据</a>
                  <a href="javascript://" @click="findApprove(scope.row)">查看审核</a>-->
                  <el-button type="text" size="small" @click="handleEdit('view', scope.row)">填报</el-button>
<!--                  <el-button
                    type="text"
                    size="small"
                    @click="findApprove(scope.row)"
                    v-if="!!scope.row.lastCheckId"
                  >审核日志
                  </el-button>-->
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      title="数据审核记录"
      :visible.sync="approve.visible"
      class="checkFeedbackDialog"
      :close-on-click-modal="true"
    >
      <el-form>
        <el-form-item label>
          <!-- DIRECT CHAT PRIMARY -->
          <div class="box box-primary direct-chat direct-chat-primary box-noborder">
            <div class="box-body">
              <!-- Conversations are loaded here -->
              <div id="checkDataPane" class="direct-chat-messages">
                <div class="direct-chat-msg" v-for="(item) in approve.checkData" :key="item.id">
                  <div class="direct-chat-info clearfix">
                    <span
                      class="direct-chat-name pull-left"
                    >{{ item.entered ? item.entered.dictLabel : '' }}({{ item.enteredAtName }})</span>
                    <span class="direct-chat-timestamp pull-right">{{ item.enteredOn }}</span>
                  </div>
                  <!-- /.direct-chat-info -->
                  <img class="direct-chat-img" :src="approve.user_avatar" alt/>
                  <!-- /.direct-chat-img -->
                  <div class="direct-chat-text direct-chat-text-mix">
                    <div class="direct-chat-text-tag" v-if="1 < item.nodeId">{{ item.checkStateName }}</div>
                    <div class="direct-chat-text-body">{{ item.checkFeedback }}</div>
                  </div>
                  <!-- /.direct-chat-text -->
                </div>
                <!-- /.direct-chat-msg -->
              </div>
            </div>
            <!-- /.box-body -->
          </div>
          <!--/.direct-chat -->
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<style type="text/css">
.upload-box {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__input {
  display: none !important;
}

div.stat-bar {
  margin-left: 16px;
  line-height: 30px;
}

div.stat-bar strong {
  margin-right: 16px;
}

div.stat-bar strong:last-child {
  margin-right: 0px;
}

/*div.dataTableContainer {
  margin: 8px auto;
}*/
</style>

<script>
import global from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";
import StringUtil from "@/utils/StringUtil";
import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";
import DateUtil from "@/utils/DateUtil";

export default {
  name: "page_uploadLogManager",

  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,
      formDisabled: true,

      dataTableHeight: "100%",

      tabs: {
        reportTask: "primary", //1缺省敏感指标任务
        infoEditTask: "", //3档案任务
        reportTask4: "", //4不良事件任务
        reportTask5: "", //5优质护理任务

        //是否管理员?
        isManager: false
      },

      tableDataAction:
        global.cmis.domain + "/carePlatform/reportResult/findList",
      statDataAction: global.cmis.domain + "/carePlatform/index/statistics",
      saveAction: global.cmis.domain + "/carePlatform/dataChange/save",

      pickerOptions: {
        shortcuts: [
          {
            text: "本月",
            onClick(picker) {
              picker.$emit("pick", [
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                ),
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                )
              ]);
            }
          },
          {
            text: "今年至今",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-01-01  00:00:00")
              );
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              start.setMonth(start.getMonth() - 6);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },

      form: {
        visible: PageUtil.searchFormVisibility(),
        combo_year: formConfig.combo_year,
        combo_quarter: formConfig.combo_quarter,
        combo_submitState: formConfig.combo_submitState,
        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_orgGrade: formConfig.combo_orgGrade,
        combo_orgClass: formConfig.combo_orgClass,
        combo_medinstCategory: formConfig.combo_medinstCategory,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgProp: formConfig.combo_orgProp,
        combo_area: formConfig.combo_areaHierarchical,
        combo_orgAttachType: formConfig.combo_orgAttachType,
        combo_jobType: formConfig.combo_jobType,
        combo_reportTemplate: formConfig.combo_reportTemplate,
        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),
        // 目前功能是设定单一时间或者全查，默认6个季度前
        // value_fromYear:
        //   DateUtil.dateAdd("q", -6, new Date()).getFullYear() + "",
        // value_fromQuarter:
        //   parseInt(DateUtil.dateAdd("q", -6, new Date()).getMonth() / 3) +
        //   1 +
        //   "",

        // value_toYear: new Date().getFullYear() + "",
        // value_toQuarter:
        //   parseInt(DateUtil.dateAdd("q", -1, new Date()).getMonth() / 3) +
        //   1 +
        //   "",
        value_searchDataTime: [],
        value_fromTime: "",
        value_year: "",
        value_quarter: "",
        value_submitState: "",
        value_orgId: [""], //lzd todo 默认查询下级,数据权限[formConfig.value_user.org_id]
        value_orgPartId: [""],
        value_orgGrade: "",
        value_orgClass: "",
        value_medinstCategory: "",
        value_nationalUpload: "",
        value_checkState: null,
        value_orgProp: "",
        value_edu: "",
        value_orgName: "",
        value_area: formConfig.value_user.org_area,
        value_orgAttachType: "",
        value_reportTemplate: null,
        value_jobType: "1", //任务类型默认为敏感数据
        value_jobPeriod: "4", //任务周期默认为季度
        filterLevel: 1
      },

      stat: {
        enable: false,
        numCommittedToday: 0,
        numTocommitPrequarter: 0,
        numCommittedPrequarter: 0,
        numRecordCommittedYestoday: 0,
        numJobType1: 0,
        numJobType3: 0,
        numJobType4: 0,
        numJobType5: 0
      },

      approve: {
        visible: false,
        user_avatar: global.cmis.domain + global.userAvatar,
        checkData: []
      }
    };
  },

  mounted: function () {
    var self = this;
    if (1 != formConfig.value_user.orgPart_id) {
      //非全院的病区管理员只看自己病区任务
      self.form.value_orgPartId = StringUtil.getIdsById(
        formConfig.value_orgPartId,
        formConfig.value_user.orgPart_id
      );
    } else if (
      //省级管理员只看全院任务
      1 == formConfig.value_user.orgPart_id &&
      1 == formConfig.value_user.org_id
    ) {
      self.form.value_orgPartId = StringUtil.getIdsById(
        formConfig.value_orgPartId,
        "1"
      );
    }
    self.layoutPage();

    self.setupPage();

    self.getData();
  },

  /**
   * 激活组件
   */
  activated: function () {
    this.getData();
  },

  methods: {
    /**
     * 读取用户角色信息
     */
    setupPage: function () {
      var vm = this;
      var ajaxOpts = {
        async: false,
        url:
          global.cmis.domain + "/carePlatform/reportResult/checkEmployeePost",
        dataType: "JSON",
        success: function (res) {
          //管理员
          if (res && res["isManager"]) {
            //是否统计
            vm.stat.enable = true;
            //允许哪些查询条件
            if ("1" == formConfig.value_user.org_id) {
              vm.form.filterLevel = 0;
              //默认审核状态
              vm.form.value_checkState = "1";
            }

            //
            vm.tabs.isManager = true;
          }
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    handleEdit(mode, row) {
      var route;
      if (row.report && 3 == row.report.jobType) {
        route = {
          name: "单位管理",
          path: "/userOrganization",
          params: {
            "reportResult.id": row["id"],
            jobType: row.report.jobType
          }
        };
      } else {
        route = {
          name: "数据填报",
          path: "/indexItemsPanels",
          params: {
            mode: mode,
            id: row["id"],
            fromTime: DateUtil.strToDate(row["dataFromTime"]),
            toTime: DateUtil.strToDate(row["dataFromTime"]), //DateUtil.dateToStr(              DateUtil.strToDate(row["dataFromTime"]),              "yyyy-MM-dd"            ),
            orgPartId: row["orgPartId"],
            orgId: row["organizationId"],
            jobType: row.report.jobType
          },
          meta: {forceReload: true}
        };
      }

      PageUtil.goto(this, route);
      //this.$router.push(route);
    },

    pushReport() {
      var self = this;

      self.tabs.reportTask = "primary";
      self.tabs.infoEditTask = "";
      self.tabs.reportTask4 = "";
      self.tabs.reportTask5 = "";

      self.form.value_jobType = "1";
      self.form.value_jobPeriod = "4";
      self.getData();
    },
    pushReport4() {
      var self = this;

      self.tabs.reportTask = "";
      self.tabs.infoEditTask = "";
      self.tabs.reportTask4 = "primary";
      self.tabs.reportTask5 = "";

      self.form.value_jobType = "4";
      self.form.value_jobPeriod = "3";
      self.getData();
    },
    pushReport5() {
      var self = this;

      self.tabs.reportTask = "";
      self.tabs.infoEditTask = "";
      self.tabs.reportTask4 = "";
      self.tabs.reportTask5 = "primary";

      self.form.value_jobType = "5";
      self.form.value_jobPeriod = "4";
      self.getData();
    },
    pushInfoEdit() {
      var self = this;

      self.tabs.reportTask = "";
      self.tabs.infoEditTask = "primary";
      self.tabs.reportTask4 = "";
      self.tabs.reportTask5 = "";

      self.form.value_jobType = "3";
      self.form.value_jobPeriod = "0";

      self.getData();
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    getData: function () {
      this.getTableData();

      this.getStatData();
    },

    saveData: function () {
      var self = this;

      //请求数据
      var ajaxOpts = {
        type: "post",
        url: self.saveAction,
        // 将数据转换为JSON字符串
        data: {
          "reportResult.dataFromTime": '2025-07-01 00:00:00',
          "reportResult.dataToTime": '2025-10-01 00:00:00'
        },
        dataType: "json",
        success: function (res) {
          res = res || {};
          console.log("保存指标任务结果", res)
          self.loading = false;
          self.$alert(res.message);
        },
        error: function () {
          self.loading = false;
          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    getStatData: function () {
      var self = this;

      //请求数据
      var ajaxOpts = {
        type: "post",
        url: self.statDataAction,
        data: {
          jobType: self.form.value_jobType
        },
        dataType: "json",
        success: function (res) {
          res = res || {};
          var data = res.data || "";

          self.stat.numJobType1 = data.numJobType1 || 0;
          self.stat.numJobType3 = data.numJobType3 || 0;
          self.stat.numJobType4 = data.numJobType4 || 0; // lzd todo
          self.stat.numJobType5 = data.numJobType5 || 0;
          self.stat.numCommittedToday = data.numCommittedToday || 0;
          self.stat.numTocommitPrequarter = data.numTocommitPrequarter || 0;
          self.stat.numCommittedPrequarter = data.numCommittedPrequarter || 0;
        },
        error: function () {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    getTableData: function () {
      var self = this;

      self.loading = true;

      //数据时间 按jobType赋值数据开始截止时间
      var dataFromTime, dataToTime;
      if ("4" == self.form.value_jobPeriod) {
        dataFromTime = self.form.value_fromTime;
        dataToTime = self.form.value_fromTime;
      } else {
        //数据时间 转换赋值
        var searchDataTime = self.form.value_searchDataTime;
        if (searchDataTime && searchDataTime.length) {
          dataFromTime = DateUtil.dateToStr(searchDataTime[0]);
          dataToTime = DateUtil.dateToStr(searchDataTime[1]);
        }
      }

      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: {withCredentials: true}, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.tableDataAction,
        data: {
          pageNo: self.currentPage,
          pageSize: self.pageSize,

          ["report.jobType"]: self.form.value_jobType,

          fromYear: self.form.value_year,
          fromQuarter: self.form.value_quarter,
          dataFromTime: dataFromTime,
          dataToTime: dataToTime,
          execStatus: self.form.value_submitState,
          //enteredAt: StringUtil.getLastNode(self.form.value_orgId),
          //orgId: StringUtil.getLastNode(self.form.value_orgId),
          orgPartId:
            self.form.value_jobType != "3"
              ? StringUtil.getLastNode(self.form.value_orgPartId)
              : "", //档案修改任务 不考虑orgPart
          enteredAtName: self.form.value_orgName,
          check: self.form.value_checkState,
          "organization.extend.extendS1": self.form.value_orgGrade,
          "organization.extend.extendS2": self.form.value_orgClass,
          "organization.extend.extendS3": self.form.value_medinstCategory,
          "organization.extend.extendS4": self.form.value_orgProp,
          "organization.extend.extendI1": self.form.value_edu,
          "organization.extend.extendF1": self.form.value_nationalUpload,
          "organization.extend.extendS5": StringUtil.getLastNode(
            self.form.value_area
          ),
          "organization.extend.extendI4": self.form.value_orgAttachType,
          "orgPart.extend.extendI4": self.form.value_reportTemplate
        },
        dataType: "json",
        success: function (res) {
          self.loading = false;

          self.tableData = res.list;
          self.total = res.count;
        },
        error: function () {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    onSearch: function () {
      this.getTableData();
    },

    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function () {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function () {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    },
    /**
     * 流程条节点状态
     */
    flowStepStatus: function (step, flow) {
      return step == flow.lastCheckNodeId && "4" == flow.lastCheckState
        ? "error"
        : "";
    },

    findApprove: function (row) {
      var self = this;

      self.approve.visible = true;

      var ajaxOpts = {
        url: global.cmis.domain + "/carePlatform/check/findList",
        dataType: "json",
        data: {"reportResult.id": row.id},
        success: function (res) {
          //
          if ("true" == res["result"]) {
            self.approve.checkData = res["data"];
            //滚动到最新审批
            setTimeout(function () {
              var checkDataPane = document.getElementById("checkDataPane"); //$("#checkDataPane").get(0);
              if (checkDataPane) {
                checkDataPane.scrollTop = checkDataPane.scrollHeight;
              }
            }, 200);
          }
        }
      };

      AjaxUtil.send(ajaxOpts);
    }
  }
};
</script>

