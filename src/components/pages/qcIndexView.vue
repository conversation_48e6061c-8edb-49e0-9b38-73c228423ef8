<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader" v-if="showPaneHead">
          <el-row class="pane-head">
            <el-col :span="21" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;指标数据查询
            </el-col>
            <el-col :span="3">
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline" :rule="form.rules">
            <el-row class="form-row">
              <el-col :span="21">
<!--                <el-form-item label>
                  <el-button-group>
                    <el-button size="mini" :type="tabs.whole" @click="pushWhole">全院</el-button>
                    <el-button size="mini" :type="tabs.ward" @click="pushWard">病区</el-button>
                    <el-button size="mini" :type="tabs.ICU" @click="pushICU">ICU</el-button>
                  </el-button-group>
                </el-form-item>-->

<!--                <el-form-item label="任务类型" class="form-h-item" prop="jobType">
                  <el-select
                    v-model="form.value_jobType"
                    placeholder="请设置"
                    size="mini"
                    @change="selectJobType"
                  >
                    <el-option
                      v-for="item in form.combo_jobType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->

                <el-form-item label="地区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>
                <!--
                <el-form-item label="单位" class="form-h-item">
                  <el-select
                    v-model="form.value_orgId"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_orgId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                                    

                -->
                <el-form-item label="单位" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>
                <!--
                <el-form-item label="科室病区" class="form-h-item">
                  <el-select v-model="form.value_orgPartId" placeholder="全院" size="mini">
                    <el-option
                      v-for="item in form.combo_orgPartId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"                      
                    ></el-option>
                  </el-select>
                </el-form-item>
                -->
                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="partUserFormDisabled"
                  ></el-cascader>
                </el-form-item>

                <!-- <el-form-item label="单位名称" class="form-h-item">
                  <el-input v-model="form.value_orgName" size="mini"></el-input>
                </el-form-item>-->
                <el-form-item label="数据日期" v-if="form.value_jobPeriod != '4'">
                  <el-date-picker
                    type="monthrange"
                    v-model="form.value_searchDataTime"
                    range-separator="——"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="mini"
                    align="right"
                    unlink-panels
                    :picker-options="pickerOptions"
                    format="yyyy 年 MM 月"
                  ></el-date-picker>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item" v-if="form.value_jobPeriod == '4'">
                  <el-select v-model="form.value_fromTime" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度" class="form-h-item" v-if="form.value_jobPeriod == '4'">
                  <el-select v-model="form.value_toTime" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

<!--                <el-form-item label="上报国家" class="form-h-item">
                  <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->

                <el-form-item label="提交状态" class="form-h-item">
                  <el-select v-model="form.value_submitState" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_submitState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

<!--                <el-form-item label="终审状态" class="form-h-item">
                  <el-select v-model="form.value_checkState" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_checkState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->

<!--                <el-form-item label="单位定级" class="form-h-item">
                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="item-view-grid"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            v-loading="loading"
            :height="dataTableHeight"
            ref="dataTable"
          >
            <el-table-column type="index" width="50" fixed />
            <template v-for="(col, key, index) in cols">
              <el-table-column
                :type="col.type"
                :prop="col.prop"
                sortable
                :label="col.label"
                :width="col.width || 90"
                v-bind:key="index"
                :fixed="col.fixed"
              >
                <template slot-scope="scope">
                  <el-tag type="primary" v-if="col.type==='sort'">{{ scope.row.type }}</el-tag>
                  <!-- <i :class="{'el-icon-check': 'currentDataIsUpload' == col.prop && scope.row[col.prop]}"></i> -->
                  <i
                    v-if="'currentDataIsUpload' == col.prop"
                    :class="{'el-icon-check': scope.row[col.prop], 'el-icon-close': !scope.row[col.prop]}"
                  ></i>
                  <span v-if="'currentDataIsUpload' != col.prop">{{ scope.row[col.prop] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style>
div.item-view-grid .el-icon-check {
  color: green;
}
div.item-view-grid .el-icon-close {
  color: red;
}
</style>

<script>
import globalVariables from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";
import StringUtil from "@/utils/StringUtil";
import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_qcIndexView",

  props: ["fatherData"],
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      dataTableHeight: "100%",
      loading: false,
      formDisabled: true,
      showPaneHead: true,
      partUserFormDisabled: false,
      tableDataAction:
        globalVariables.cmis.domain + "/carePlatform/qcIndex/view",
      //tableDataAction: "http://127.0.0.1:8880/rh-cmis/carePlatform/qcIndex/view",
      tabs: {
        whole: "primary", //全院模版 1
        Ward: "", //病区模板  2
        ICU: "" //ICU模版  3
      },

      pickerOptions: {
        shortcuts: [
          {
            text: "本月",
            onClick(picker) {
              picker.$emit("pick", [
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                ),
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                )
              ]);
            }
          },
          {
            text: "今年至今",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-01-01  00:00:00")
              );
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              start.setMonth(start.getMonth() - 6);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },

      form: {
        visible: PageUtil.searchFormVisibility(),

        combo_submitState: formConfig.combo_submitState,
        //lzd todo, 默认值设置，提交时赋值，因为 多级选择框的值格式是：2080,2105,2106
        /*combo_orgPartId: (function() {
          var tmp = formConfig.combo_orgPartIdHierarchical.slice(0);
          if (tmp[0].label == "所有") tmp.shift();
          return tmp;
        })(),*/

        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_area: formConfig.combo_areaHierarchical,

        combo_orgGrade: formConfig.combo_orgGrade,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),
        combo_jobType: formConfig.combo_jobType,
        // combo_jobType: [
        //   { value: "1", label: "敏感指标任务" },
        //   { value: "4", label: "不良事件任务" },
        //   { value: "5", label: "优质护理任务" }
        //   //,{ value: "3", label: "档案任务" }
        // ],
        value_searchDataTime: [],
        value_fromTime: "",
        value_toTime: "",
        value_submitState: "",
        value_enteredAt: formConfig.value_user.org_id,
        value_orgId: [formConfig.value_user.org_id], //lzd todo 默认查询下级,数据权限
        value_orgPartId: [""], //默认全院formConfig.value_user.orgPart_tree_ids,

        value_orgName: "",
        value_orgGrade: "",
        value_checkState: "",
        value_nationalUpload: "",
        value_area: formConfig.value_user.org_area,
        value_reportTemplate: formConfig.value_user.hasOwnProperty("orgPart_id")
          ? formConfig.value_user.orgPart_template_id
          : 1,
        value_jobType: "1", //任务类型默认为敏感数据
        value_jobPeriod: "4", //任务周期默认为季度
        rules: {
          value_fromTime: [{ required: true, message: "请选择开始时间" }],
          value_toTime: [{ required: true, message: "请选择截止时间" }],
          value_searchDataTime: [
            { required: true, message: "请选择开始截止时间" }
          ]
        }
      },
      cols: [
        {
          label: "省份",
          prop: "province",
          type: "normal",
          width: 120 //,
          //fixed: true
        },
        {
          label: "地市",
          prop: "city",
          type: "normal",
          width: 60 //,
          //fixed: true
        },
        {
          label: "医院名称",
          prop: "org",
          type: "normal",
          width: 220,
          fixed: true
        },
        {
          label: "采集范围",
          prop: "orgPart",
          type: "normal",
          width: 100,
          fixed: true
        },
        { label: "医院定级", prop: "orgGrade", type: "normal", width: 120 },
        { label: "医院定等", prop: "orgClass", type: "normal", width: 120 },
        { label: "是否教学", prop: "isEducation", type: "normal", width: 120 },
        {
          label: "医院性质",
          prop: "hospitalNature",
          type: "normal",
          width: 120
        },
        { label: "医院单位码", prop: "orgCode", type: "normal", width: 160 },
        {
          label: "医院类别",
          prop: "medinstCategory",
          type: "normal",
          width: 120
        },
        {
          label: "编制床位数",
          prop: "fixedBedNum",
          type: "normal",
          width: 130
        },
        {
          label: "注册护士数",
          prop: "regNursesNum",
          type: "normal",
          width: 130
        },
        {
          label: "护理部电话",
          prop: "nursingDepartPhone",
          type: "normal",
          width: 130
        },
        {
          label: "负责人姓名",
          prop: "managerName",
          type: "normal",
          width: 160
        },
        {
          label: "负责人电话",
          prop: "managerPhone",
          type: "normal",
          width: 160
        },
        { label: "邮箱", prop: "managerEmail", type: "normal", width: 200 },
        {
          label: "单位审核通过时间",
          prop: "orgApprovalTime",
          type: "normal",
          width: 180
        },
        { label: "是否冻结", prop: "orgIsFrozen", type: "normal", width: 120 },
        {
          label: "最近一次冻结时间",
          prop: "orgLastFrozenTime",
          type: "normal",
          width: 180
        },
        {
          label: "最近一次解冻时间",
          prop: "orgLastUnfrozenTime",
          type: "normal",
          width: 180
        },
        {
          label: "开始填报时间",
          prop: "firstDataQuarter",
          type: "normal",
          width: 160
        },
        {
          label: "当前需填报时间",
          prop: "currentDataIsUpload",
          type: "normal",
          width: 180
        },
        {
          label: "是否进入样板库",
          prop: "isTemplateData",
          type: "normal",
          width: 140
        },
        {
          label: "填报时间",
          prop: "currentDataQuarter",
          type: "normal",
          width: 140,
          fixed: true
        },
        { label: "提交状态", prop: "submitState", type: "normal", width: 140 },
        /*{
          label: "审核状态",
          prop: "checkState",
          type: "normal",
          width: 140,
          fixed: true
        }*/
      ],
      tableData: []
    };
  },

  mounted: function() {
    var self = this;

    self.layoutPage();
    //如果作为别人的子组件则隐藏头部标题
    if (null != self.fatherData) {
      self.showPaneHead = self.fatherData;
    }
    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    }

    var now = DateUtil.dateAdd("q", -1, new Date());
    //初始开始, 结束时间
    //self.form.value_fromTime = now.getFullYear() + "-01-01";
    var from = DateUtil.dateAdd("q", -4, now);
    self.form.value_fromTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(from),
      "yyyy-MM-dd"
    );
    self.form.value_toTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(now),
      "yyyy-MM-dd"
    );

    var tempFromTime = new Date();
    tempFromTime = DateUtil.strToDate(
      DateUtil.dateToStr(tempFromTime, "yyyy-MM-01  00:00:00")
    );
    var tempTimeSpace = [DateUtil.dateAdd("m", -3, tempFromTime), tempFromTime];

    self.form.value_searchDataTime = tempTimeSpace;

    //初始化 单位、病区
    self.form["value_orgId"] = StringUtil.getIdsById(
      formConfig.value_orgId,
      formConfig.value_user.org_id
    );

    // self.form["value_orgPartId"] = StringUtil.getIdsById(
    //   formConfig.value_orgPartId,
    //   formConfig.value_user.orgPart_id
    // );
    // //如果是医院管理员manager 就可以编辑病区
    // if (1 == formConfig.value_user.orgPart_id) {
    //   self.partUserFormDisabled = false;
    // }

    //参数
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);
    //
    var k, v;
    for (k in formValues) {
      v = formValues[k];
      if (!v) {
        continue;
      }
      if ("area" == k) {
        v = StringUtil.getIdsById(formConfig.value_areaId, v);
      } else if ("orgPartId" == k) {
        self.form.value_reportTemplate =
          formConfig.value_orgPartId[v].templateId;
        v = StringUtil.getIdsById(formConfig.value_orgPartId, v);
      } else if ("orgId" == k) {
        v = StringUtil.getIdsById(formConfig.value_orgId, v);
      }
      self.form["value_" + k] = v;
    }
    self.form.value_jobType += "";
    self.form.value_jobPeriod =
      formConfig.value_jobType[self.form.value_jobType].period;
    //省级管理员不指定机构
    if (1 == formConfig.value_user.org_id) {
      self.form["value_orgId"] = [""];
    }
    //接受参数传入数据的开始时间与结束时间
    if (null != formValues["fromTime"]) {
      tempTimeSpace[0] = new Date(formValues["fromTime"]);
      self.form.value_fromTime = DateUtil.dateToStr(
        DateUtil.quarterHeadDate(new Date(formValues["fromTime"])),
        "yyyy-MM-dd"
      );
    }
    if (null != formValues["toTime"]) {
      tempTimeSpace[1] = new Date(formValues["toTime"]);
      self.form.value_toTime = DateUtil.dateToStr(
        DateUtil.quarterHeadDate(new Date(formValues["toTime"])),
        "yyyy-MM-dd"
      );
    }
    self.form.value_searchDataTime = tempTimeSpace;
    //alert("test2   " + self.form.value_fromTime);

    //
    //通过科室找到上报模板
    if (null != self.form.value_orgPartId) {
      switch (self.form.value_reportTemplate) {
        case 1:
          self.pushWhole();
          break;
        case 2:
          self.pushWard();
          break;
        case 3:
          self.pushICU();
          break;
      }
    } else this.getTableData();
  },

  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    pushWhole() {
      var self = this;

      self.tabs.whole = "primary";
      self.tabs.ICU = "";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 1;
      self.getTableData();
    },

    pushWard() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "";
      self.tabs.ward = "primary";
      self.form.value_reportTemplate = 2;
      self.getTableData();
    },

    pushICU() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "primary";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 3;
      self.getTableData();
    },

    getTableData: function() {
      var self = this;
      self.loading = true;
      var lastNode = StringUtil.getLastNode(self.form.value_area);
      //表单验证 按jobType赋值数据开始截止时间
      var fromTime, toTime;
      if ("4" == self.form.value_jobPeriod) {
        fromTime = self.form.value_fromTime;
        toTime = self.form.value_toTime;
      } else {
        //数据时间 转换赋值
        fromTime = DateUtil.dateToStr(self.form.value_searchDataTime[0]);
        toTime = DateUtil.dateToStr(self.form.value_searchDataTime[1]);
      }

      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        type: "post",
        url: self.tableDataAction,
        data: {
          pageNo: self.currentPage,
          pageSize: self.pageSize,

          orgId: StringUtil.getLastNode(self.form.value_orgId),
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          org: self.form.value_orgName,
          checkState: self.form.value_checkState,
          orgGrade: self.form.value_orgGrade,
          submitState: self.form.value_submitState,
          nationalUpload: self.form.value_nationalUpload,
          "quarterRange.fromTime": fromTime,
          "quarterRange.toTime": toTime,
          "orgPartObj.extend.extendI4": self.form.value_reportTemplate,
          area: StringUtil.getTreeNames(formConfig.value_areaId, lastNode),
          jobType: self.form.value_jobType
          // formConfig.value_areaId[
          //   StringUtil.getLastNode(self.form.value_area)
          // ].treeNames
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;

          self.tableData = res.list;
          self.total = res.count;
          //读取指标
          self.getIndexItems();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 读取指标
     */
    getIndexItems: function() {
      var self = this;
      //是否有单位档案
      if (0 >= self.total) {
        return;
      }
      //保留前23列, 单位档案
      var colDocNum = 26;
      var cols = self.cols.slice(0, colDocNum);
      self.cols = cols;

      //self.$alert(self.form.value_toTime);
      //表单验证 按jobType赋值数据开始截止时间
      var fromTime, toTime;
      if ("4" == self.form.value_jobPeriod) {
        //表单验证
        if (!self.form.value_fromTime || !self.form.value_toTime) {
          self.$alert('请选择"开始时间"和"截止时间".');
          return;
        }
        fromTime = self.form.value_fromTime;
        toTime = self.form.value_toTime;
      } else {
        //表单验证
        if (!self.form.value_searchDataTime) {
          self.$alert('请选择"开始时间"和"截止时间".');
          return;
        }
        //数据时间 转换赋值
        fromTime = DateUtil.dateToStr(self.form.value_searchDataTime[0]);
        toTime = DateUtil.dateToStr(self.form.value_searchDataTime[1]);
      }
      //console.log(self.form);
      //
      var ajaxOptsIdx = {
        url: globalVariables.cmis.domain + "/carePlatform/index/getItemsList",
        //url: 'http://127.0.0.1:8880/rh-cmis/carePlatform/index/getItemsList'
        type: "post",
        dataType: "json",
        data: {
          enteredAt: self.form.value_enteredAt,
          orgId: StringUtil.getLastNode(self.form.value_orgId),
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "quarterRange.fromTime": fromTime,
          "quarterRange.toTime": toTime,
          "orgPart.extend.extendI4": self.form.value_reportTemplate,
          itemTypeName: "指标数据",
          jobType: self.form.value_jobType
        },
        complete: function() {
          self.loading = false;
        },
        success: function(res) {
          var idxItems = res ? res["data"] : null;
          if (!idxItems || !idxItems.length) {
            return;
          }
          var tableData = self.tableData;
          var re = /\./;
          for (var j = 0, itm, prop, iprop; j < idxItems.length; j++) {
            itm = idxItems[j];

            if (!re.test(itm["code"])) {
              //过滤掉code不含'.'的二级标题
              //console.log(itm);
              continue;
            }
            //
            prop = "index" + itm["code"]; //itm["id"];
            iprop = false; //是否有了这个指标列
            for (var j1 = colDocNum; j1 < cols.length; j1++) {
              if ((iprop = prop == cols[j1]["prop"])) {
                break;
              }
            }
            if (!iprop) {
              cols.push({
                label: itm["name"],
                prop: prop,
                type: "normal",
                width: 180
              });
            }
            if (!itm["fromTime"]) {
              continue;
            }
            //
            var dt = DateUtil.quarterLabelOfDate(
              DateUtil.strToDate(itm["fromTime"])
            );
            //
            for (var k = 0, tr; k < tableData.length; k++) {
              tr = tableData[k];
              //对比单位, 可是病区, 填报季度
              if (
                tr["orgId"] == itm["organizationId"] &&
                tr["orgPartId"] == itm["orgPartId"]
              ) {
                if (tr["currentDataQuarter"] == dt) {
                  tr["currentDataIsUpload"] = 1;
                  tr[prop] = itm["value"];
                }
              }
            }
          }
          self.cols = cols;

          setTimeout(function() {
            self.$refs.dataTable && self.$refs.dataTable.doLayout();
          }, 0);
        }
      };
      //
      self.loading = true;
      //
      AjaxUtil.send(ajaxOptsIdx);
    },

    onSearch: function() {
      this.getTableData();
    },

    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    },

    selectJobType: function(params) {
      self = this;
      // params = params.split(",");
      // let value = params[0]; // 获取到的value
      // let label = params[1]; // 获取到的label
      //var lastjobPeriod = self.form.value_jobPeriod;
      self.form.value_jobPeriod =
        formConfig.value_jobType[self.form.value_jobType].period;
      // if ("4" == self.form.value_jobPeriod && "4" != lastjobPeriod) {
      // }
    }
  }
};
</script>

