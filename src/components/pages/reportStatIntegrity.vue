<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div>
          <el-row class="pane-head">
            <el-col :span="6" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;上报完整率
            </el-col>
            <el-col :span="8">
              <el-button size="mini" class="tab-button" :type="tabs.onQuarter">按季度</el-button>
              <el-button size="mini" class="tab-button" :type="tabs.onYear">按年度</el-button>
            </el-col>
            <el-col :span="10">
              <el-button size="mini">导出</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="地区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="开始年份" class="form-h-item">
                  <el-select v-model="form.value_fromYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="截止年份" class="form-h-item">
                  <el-select v-model="form.value_toYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度" class="form-h-item">
                  <el-select v-model="form.value_toQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_toQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位定级" class="form-h-item">
                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="上报国家" class="form-h-item">
                  <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="审核状态" class="form-h-item">
                  <el-select v-model="form.value_checkState" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_checkState"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="冻结时间>">
                  <el-date-picker
                    type="date"
                    v-model="form.value_lockOn"
                    placeholder="选择日期"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="tb-edit"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            @row-click="handleRowClick"
            v-loading="loading"
          >
            <!--
            <el-table-column type="index" width="50"/>-->
            <el-table-column label="序号" prop="no" width="60" />
            <el-table-column label="省份" prop="province" width="160" />
            <el-table-column label="地市" prop="city" width="160" />
            <el-table-column label="科室病区" prop="orgPart" width="160" />
            <el-table-column label="数据时间" prop="dataFromQuarter" width="160" />
            <el-table-column label="应报医院数" prop="numOrg" width="96" />
            <el-table-column label="完成医院数" prop="numOrgDone" width="96" />
            <el-table-column label="未上报医院数" prop="numOrgUndone" width="96" />
            <el-table-column label="上报完整率" prop="integrityRate" width="96">
              <template slot-scope="scope">
                <span>{{scope.row["integrityRate"]*100 | rounding}}%</span>
              </template>
            </el-table-column>

            <!-- <el-table-column label="操作" prop="maxVal">
              <template slot-scope="scope">
                <a href="###" @click="handleMain('view', scope.row)">查看任务明细</a>
              </template>
            </el-table-column>-->
          </el-table>
        </div>
        <!--
        <div class="paginationClass">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
        -->
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
.data-body {
  padding: 0;
}

.pane-head-label {
  line-height: 30px;
}

.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

.upload-box {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__input {
  display: none !important;
}
.form-row {
  margin: 4px 0;
}
.form-h-item .el-form-item__label {
  width: 5.2em;
}
.form-h-item .el-form-item__content {
  width: 160px;
}
.stat-bar {
  margin-left: 16px;
}
.stat-bar strong {
  margin-right: 8px;
}
</style>

<script>
import global from "@/config/global-config";
import orgTree from "@/config/orgTree-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import StringUtil from "@/utils/StringUtil";

import formConfig from "@/config/uploadLog-config";
import PageUtil from "@/utils/PageUtil";
export default {
  name: "page_reportStatIntegrity",

  data() {
    return {
      user: {
        roles: [],
        canStat: false,
        canEdit: false
      },
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,

      tableDataAction:
        global.cmis.domain + "/carePlatform/index/reportIntegrity",
      tabs: {
        onQuarter: "primary",
        onYear: ""
      },
      form: {
        visible: true,

        combo_fromYear: formConfig.combo_year,
        combo_fromQuarter: formConfig.combo_quarter,
        combo_toYear: formConfig.combo_year,
        combo_toQuarter: formConfig.combo_quarter,
        combo_area: formConfig.combo_areaHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgGrade: formConfig.combo_orgGrade,
        cityMap: (function() {
          var citys = {};
          for (var city in formConfig.combo_areaHierarchical) {
          }
        })(),
        // 默认6个月前
        value_fromYear:
          new Date(
            new Date().setDate(new Date().getDate() - 540)
          ).getFullYear() + "",
        value_fromQuarter:
          parseInt(
            new Date(
              new Date().setDate(new Date().getDate() - 540)
            ).getMonth() / 3
          ) +
          1 +
          "",

        value_toYear: new Date().getFullYear() + "",
        value_toQuarter:
          parseInt(
            new Date(new Date().setDate(new Date().getDate() - 90)).getMonth() /
              3
          ) +
          1 +
          "",

        value_area: [],
        value_orgPartId: ["1"],
        value_nationalUpload: "",
        value_checkState: "2", //通过审核
        value_orgGrade: "",
        value_lockOn: "" //冻结时间
      }
    };
  },

  mounted: function() {
    var self = this;

    self.getTableData();

    self.setupUserPermission();
  },
  filters: {
    rounding(value) {
      if (typeof value == "number") {
        if (Number.isNaN(value)) {
        } else {
          return value.toFixed(1);
        }
      } else {
        return value;
      }
    }
  },
  methods: {
    handleRowClick(row, event, column) {
      var self = this;
      //timeStr 2019年1季度
      var regExp = /[0-9]+/g;
      var timeObj = row.dataFromQuarter.match(regExp);
      if (timeObj == null || timeObj.length != 2) {
        self.$alert("时间[" + row.timeStr + "]不正确!");
        return;
      }
      var fromDate = new Date(timeObj[0], (timeObj[1] - 1) * 3, 1);
      var fromStr = DateUtil.dateToStr(fromDate, "yyyy-MM-dd");
      var toDate = new Date(timeObj[0], timeObj[1] * 3, 1);
      var toStr = DateUtil.dateToStr(toDate, "yyyy-MM-dd");

      // console.log(
      //   "lftest>>>>>indexIntegrityRate " +
      //     fromStr +
      //     "," +
      //     row.city +
      //     "," +
      //     formConfig.value_areaName[row.city].id
      // );
      // this.$router.push({
      //   name: "原始数据查询",
      //   params: { fromTime: fromStr, toTime: fromStr },
      //   query: {
      //     orgPartId: this.form.value_orgPartId,
      //     area: formConfig.value_areaName[row.city].id
      //   }
      // });

      var route = {
        name: "原始数据查询",
        path: "/indexItemsPanels",
        params: { fromTime: fromStr, toTime: fromStr },
        query: {
          orgPartId: this.form.value_orgPartId,
          area: formConfig.value_areaName[row.city].id
        },
        meta: { forceReload: true }
      };
      PageUtil.goto(this, route);
    },
    handleMain(mode, row) {
      var self = this;
      self.$router.push({
        name: "page_main",
        params: {
          value_fromYear: self.form.value_fromYear,
          value_fromQuarter: self.form.value_fromQuarter,
          value_toYear: self.form.value_fromYear,
          value_toQuarter: self.form.value_fromQuarter,
          value_orgPartId: self.form.value_orgPartId,
          value_yesno: self.form.value_yesno,
          value_BedNumberScope: self.form.value_BedNumberScope,
          value_areaType: self.form.value_areaType,
          value_medinstCategory: self.form.value_medinstCategory,
          value_orgProp: self.form.value_orgProp,
          value_orgGrade: self.form.value_orgGrade,
          value_orgAttachType: self.form.value_orgAttachType,
          value_clinicaledutype: self.form.value_clinicaledutype,
          value_indexId: row.idxId,
          value_orgId: self.form.value_orgId
        }
      });
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    getTableData: function() {
      var self = this;

      self.loading = true;
      //地区选择
      var value_area = self.form.value_area;
      value_area =
        value_area && value_area.length
          ? value_area[value_area.length - 1]
          : "";

      var value_lockOn = "";
      if ("" != self.form.value_lockOn && null != self.form.value_lockOn) {
        value_lockOn = DateUtil.dateToStr(self.form.value_lockOn);
      }

      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.tableDataAction,
        data: {
          //pageNo: self.currentPage,
          //pageSize: self.pageSize,

          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_toYear,
          toQuarter: self.form.value_toQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "organization.extend.extendF1": self.form.value_nationalUpload,
          check: self.form.value_checkState,
          "organization.extend.extendS1": self.form.value_orgGrade,
          "organization.extend.extendS5": value_area,
          dataPeriod: "4",
          lockOn: value_lockOn
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;

          self.tableData = res.data;
          self.total = res.count;
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    onSearch: function() {
      this.getTableData();
    },

    onChangeOrg: function(data) {
      this.getTableData();
    },

    /**
     * 用户权限: 管理员|单位人员
     */
    setupUserPermission: function() {
      var self = this;

      var userInfo = LoginHelper.logincheck(),
        usercode = userInfo ? userInfo.usercode : null;
      //权限, 临时
      //self.user.canStat = usercode && /^(system|bak[0-9]+)$/.test(usercode);
      //self.user.canEdit = usercode && /^(cdr[0-9]+)$/.test(usercode);
      var ajaxOpts = {
        async: false,
        url: global.cmis.domain + "/carePlatform/index/checkRights",
        type: "post",
        dataType: "json",
        data: { code: usercode },
        success: function(res) {
          if (res) {
            self.user.canStat = !!res.canStat;
            self.user.canEdit = !!res.canEdit;
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    }
  }
};
</script>

