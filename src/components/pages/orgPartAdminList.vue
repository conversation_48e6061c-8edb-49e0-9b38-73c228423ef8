<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="16" class="pane-head-label">
              <i class="el-icon-menu"></i>
              {{$route.name}}
            </el-col>
            <el-col :span="8">
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="18">
                <el-form-item label="姓名" class="form-h-item">
                  <el-input size="mini" v-model="form.userName" placeholder="姓名"></el-input>
                </el-form-item>

                <el-form-item label="手机号" class="form-h-item">
                  <el-input size="mini" v-model="form.mobile" placeholder="手机号"></el-input>
                </el-form-item>

                <el-form-item label="固定电话" class="form-h-item">
                  <el-input size="mini" v-model="form.phone" placeholder="固定电话"></el-input>
                </el-form-item>

                <el-form-item label="邮箱" class="form-h-item">
                  <el-input size="mini" v-model="form.email" placeholder="邮箱"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" @click="edit()" size="mini">新建病区管理员</el-button>
                  <el-button type="primary" @click="onSearch" size="mini">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <div class="dataTableContainer">
            <el-table
              :data="tableData"
              class="tb-edit"
              style="width: 100%"
              highlight-current-row
              stripe
              border
              size="mini"
              v-loading="loading"
              :height="dataTableHeight"
            >
              <el-table-column type="index" width="50" />
              <el-table-column label="所属单位" prop="employee.company.fullName" width="160">
                <template
                  slot-scope="scope"
                >{{scope.row.employee.company.fullName || scope.row.employee.company.companyName}}</template>
              </el-table-column>
              <el-table-column label="所属病区" prop="employee.office.fullName">
                <template
                  slot-scope="scope"
                >{{scope.row.employee.office.fullName || scope.row.employee.office.officeName}}</template>
              </el-table-column>
              <el-table-column label="姓名" prop="userName" width="160" />
              <el-table-column label="登录用户名" prop="loginCode" width="160" />
              <el-table-column label="手机号" prop="mobile" width="160" />
              <el-table-column label="病区固定电话" prop="phone" width="160" />
              <el-table-column label="邮箱" prop="email" width="160" />
              <el-table-column label="最后登录时间" prop="fromTime" width="160" />
              <el-table-column label="操作" width="160">
                <template slot-scope="scope">
                  <a href="javascript://" @click="edit(scope.row, 'view')">查看</a>
                  <a href="javascript://" @click="edit(scope.row)">编辑</a>
                  <a href="javascript://" @click.prevent="confirmToDo('delete', scope.row)">删除</a>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      title="新增病区管理员"
      :visible.sync="editForm.visible"
      width="600px"
      slot="footer"
      class="checkFeedbackDialog editFormDialog"
      :close-on-click-modal="false"
    >
      <div style="margin:0 auto;width:64%;">
        <el-form
          ref="editForm"
          :model="editForm"
          :rules="editForm.rules"
          :disabled="!editForm.editable"
          label-width="110px"
        >
          <el-form-item label="所属单位" size="mini" required>
            <el-cascader
              v-model="editForm.orgId"
              placeholder="试试搜索"
              :options="editForm.combo_orgId"
              filterable
              change-on-select
              :show-all-levels="true"
              clearable
            ></el-cascader>
          </el-form-item>

          <el-form-item label="科室病区" size="mini" required>
            <el-cascader
              v-model="editForm.orgPartId"
              placeholder="试试搜索"
              :options="editForm.combo_orgPartId"
              filterable
              change-on-select
              :show-all-levels="true"
              clearable
            ></el-cascader>
          </el-form-item>

          <el-form-item label="姓名" size="mini" required>
            <el-input size="mini" v-model="editForm.userName" placeholder="姓名"></el-input>
          </el-form-item>

          <el-form-item label="手机号" size="mini" required>
            <el-input size="mini" v-model="editForm.mobile" placeholder="手机号"></el-input>
          </el-form-item>

          <el-form-item label="病区固定电话" size="mini" required>
            <el-input size="mini" v-model="editForm.phone" placeholder="病区固定电话"></el-input>
          </el-form-item>

          <el-form-item label="邮箱" size="mini" required>
            <el-input size="mini" v-model="editForm.email" placeholder="邮箱"></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="editForm.visible = false">取 消</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="confirmToDo('save')"
            v-if="editForm.editable"
          >确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import me from "@/assets/js/pages/orgPartAdminList";

export default me;
</script>