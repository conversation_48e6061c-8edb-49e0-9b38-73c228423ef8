<template>
  <div>
    <el-container>
      <el-header style="padding:0;height:auto;">
        <div>
          <el-row class="pane-head">
            <el-col :span="6" class="pane-head-label" id="headName">
              <i class="el-icon-menu"></i>指标总览
            </el-col>
            <el-col :span="8">
              <el-button
                size="mini"
                class="tab-button"
                :type="tabCitys.selfCityType"
                @click="getSelfCity"
              >本市</el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabCitys.selfProvType"
                @click="getSelfProvince"
              >本省</el-button>
            </el-col>
            <el-col :span="10">
              <el-button size="mini">导出</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="search-form-pane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="21">
                <!-- <el-form-item label="单位" class="form-h-item">
                  <el-select
                    v-model="form.value_orgId"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_orgId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->
                <!-- <el-form-item label>
                  <el-button-group>
                    <el-button size="mini" :type="tabs.whole" @click="pushWhole">全院</el-button>
                    <el-button size="mini" :type="tabs.ward" @click="pushWard">病区</el-button>
                    <el-button size="mini" :type="tabs.ICU" @click="pushICU">ICU</el-button>
                  </el-button-group>
                </el-form-item> -->
                <el-form-item label="单位" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>
                <el-form-item label="科室病区" class="form-h-item" >
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="partUserFormDisabled"
                    v-on:change="changeOrgpart"
                  ></el-cascader>
                </el-form-item>
                <el-form-item label="开始年份" class="form-h-item">
                  <el-select v-model="form.value_fromYear" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromYear"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromQuarter" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_fromQuarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位类型" class="form-h-item">
                  <el-select v-model="form.value_medinstCategory" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_medinstCategory"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="所有制" class="form-h-item" >
                  <el-select v-model="form.value_orgProp" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgProp"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="隶属类型" class="form-h-item">
                  <el-select v-model="form.value_orgAttachType" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgAttachType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="单位定级" class="form-h-item">
                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgGrade"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="床位数" class="form-h-item">
                  <el-select v-model="form.value_BedNumberScope" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_BedNumberScope"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="上报国家" class="form-h-item">
                  <el-select v-model="form.value_yesno" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_yesno"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="临床教学基地类型" class="form-h-item" label-width="140px">
                  <el-select v-model="form.value_clinicaledutype" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_clinicaledutype"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableDatas">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-header>
      <el-main style="padding:0" class="data-body">
        <div class="content-pane" style="width:99.4%;min-width:680px">
          <el-table
            :data="tableAverMedData"
            class="tb-edit"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            @row-click="handleRowClick"
            v-loading="loading"
          >
            <el-table-column type="index" width="50"/>
            <el-table-column label="指标" prop="idxName" width/>
            <el-table-column label="本院指标值" prop="idxVal" width="160"/>
            <el-table-column label="中位数" prop="quantile50" width="160"/>
            <!-- <el-table-column label="最小值" prop="minVal" width="160"/> -->
            <!-- <el-table-column label="10%分位数" prop="quantile10" width="160"/> -->
            <el-table-column label="下四分位数" prop="quantile25" width="160"/>
            <el-table-column label="上四分位数" prop="quantile75" width="160"/>
            <!-- <el-table-column label="90%分位数" prop="quantile90" width="160"/> -->
            <!-- <el-table-column label="最大值" prop="maxVal" width="160"/> -->
            <el-table-column label="符合条件科室数" prop="departCnt" width="160"/>
            <el-table-column label="操作" prop="maxVal" width="160">
              <template slot-scope="scope">
                <a href="###" @click="handleView('view', scope.row)">查看图表</a>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
aside.el-aside {
  background-color: #d3dce6;
  color: #333;
  text-align: center;
  line-height: 200px;
}

/* 图形 */
div.viewWrapper {
  padding: 2px;
}
.viewWrapper .row {
  margin-left: 0;
  margin-right: 0;
}
.viewWrapper .box-widget {
  margin-bottom: 2px;
}
.viewWrapper .box-header .box-title {
  display: block;
  margin: 0 0px;
  text-align: center;
}
.viewWrapper .box-body {
  padding: 1px;
}

.viewWrapper .col-md-12,
.viewWrapper .col-md-5,
.viewWrapper .col-md-7 {
  padding-right: 2px;
  padding-left: 2px;
}

.viewDataSheet .ui-widget-content {
  border-width: 0;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-hdiv {
  border-color: #949397;
}
.viewDataSheet .ui-jqgrid-htable thead tr,
.viewDataSheet .ui-jqgrid-hdiv,
.viewDataSheet .ui-jqgrid-hbox {
  background-color: #6b7984;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-labels th {
  border-color: #949397 !important;
}
.viewDataSheet .ui-jqgrid-sortable {
  color: #f9fefe;
}

.viewDataSheet .ui-jqgrid-bdiv .ui-priority-secondary {
  background-color: #d2e4e6;
}
.fa-red {
  color: red;
}
.fa-green {
  color: green;
}
</style>

<script>
import global from "@/config/global-config";
import orgTree from "@/config/orgTree-config";
import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import StringUtil from "@/utils/StringUtil";

import formConfig from "@/config/uploadLog-config";
import indexConfig from "@/config/index-config";
import PageUtil from "@/utils/PageUtil";
// import echarts from "echarts";

export default {
  name: "page_indexOverview",
  
  data() {
    return {
      user: {
        roles: [],
        canStat: false,
        canEdit: false
      },
      tabCitys: {
        selfCityType: "primary",
        selfProvType: ""
      },

      tabs: {
        whole: "primary", //全院模版 1
        Ward: "", //病区模板  2
        ICU: "" //ICU模版  3
      },
      total: 0,
      currentPage: 1,
      pageSize: 20,
      averMedianData: [],
      tableAverMedData: [],
      groupPercentData: [],
      tableGroupPercentData: [],
      loading: false,
      formDisabled: true,
      partUserFormDisabled: true,
      cols: [],
      AverMedianDataAction:
        global.cmis.domain + "/carePlatform/index/getIndexAverageMedian",
      form: {
        visible: true,
        groupVisible: false,
        //menu_index: indexConfig["groupIdx"],
        menu_index:[],
        menu_map: indexConfig["idx"],
        menu_group: indexConfig["pgIdx"],
        combo_fromYear: formConfig.combo_year,
        combo_fromQuarter: formConfig.combo_quarter,
        combo_toYear: formConfig.combo_year,
        combo_toQuarter: formConfig.combo_quarter,
        combo_area: formConfig.combo_areaHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgGrade: formConfig.combo_orgGrade,
        combo_medinstCategory: formConfig.combo_medinstCategory,
        combo_orgProp: formConfig.combo_orgProp,
        combo_orgAttachType: formConfig.combo_orgAttachType,
        combo_BedNumberScope: formConfig.combo_BedNumberScope,
        combo_clinicaledutype: formConfig.combo_clinicaledutype,
        combo_orgId: formConfig.combo_orgIdHierarchical,

        value_fromYear:
          DateUtil.dateAdd("q", -1, new Date()).getFullYear() + "",
        value_fromQuarter:
          parseInt(DateUtil.dateAdd("q", -1, new Date()).getMonth() / 3) +
          1 +
          "",
        value_toYear: new Date().getFullYear() + "",
        value_toQuarter:
          parseInt(
            DateUtil.dateAdd("q", -1, new Date()).getMonth() /
              3
          ) +
          1 +
          "",

        value_orgId: [], //单位
        value_area: formConfig.value_user.org_area,
        value_areaType: 1, //本市 1 本省 0
        value_yesno: "", //是否上报国家
        value_orgGrade: "", //单位定级
        value_orgProp: "", //所有制性质
        value_orgPartLable: "全院", //科室病区
        value_orgPartId: ["1"], //科室病区
        value_medinstCategory: "", //单位类型
        value_orgAttachType: "", //隶属类型
        value_BedNumberScope: "", //床位数
        value_clinicaledutype: "", //临床教学基地类型
        value_indexId: "13", //指标,缺省全院床护比
        value_groupIdx: "", //分组指标
        value_hospital: "",
        value_reportTemplate:formConfig.value_user.hasOwnProperty("orgPart_template_id")?formConfig.value_user.orgPart_template_id:1
      }
    };
  },

  mounted: function() {
    var self = this;
    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    } else {
      self.form.value_orgId = [formConfig.value_user.org_id];
      self.form.value_orgGrade = formConfig.value_user.org_grade;
      //value_hospital = formConfig.value_user.org_name;
    }
    self.form.headName = "全院床护比";
    //初始化 单位、病区
    self.form["value_orgPartId"] = StringUtil.getIdsById(
      formConfig.value_orgPartId,
      formConfig.value_user.orgPart_id
    );
    //如果是医院管理员manager 就可以编辑病区
    if (1 == formConfig.value_user.orgPart_id) {
      self.partUserFormDisabled = false;
    }
    self.getTableDatas();

    //self.getDetailTableData();

    self.setupUserPermission();
  },

  methods: {
    handleRowClick(row, event, column) {
      var self = this;
      //timeStr 2019年1季度
      var regExp = /[0-9]+/g;
      var timeObj = row.timeStr.match(regExp);
      if (timeObj == null || timeObj.length != 2) {
        self.$alert("时间[" + row.timeStr + "]不正确!");
        return;
      }
      var fromDate = new Date(timeObj[0], (timeObj[1] - 1) * 3, 1);
      var fromStr = DateUtil.dateToStr(fromDate, "yyyy-MM-dd");
      var toDate = new Date(timeObj[0], timeObj[1] * 3, 1);
      var toStr = DateUtil.dateToStr(toDate, "yyyy-MM-dd");
      //console.log("lftest>>>>>" + fromStr + "," + toStr);
      var area;
      if (self.form.value_area.length == 0) {
      } else {
        area = self.form.value_area[0];
        if (self.form.value_areaType == 0 && self.form.value_area.length >= 2)
          area = self.form.value_area[1];
      }

      //query:{orgPartId:"1",area:'2080'}});
    },

    handleView(mode, row) {
      var self = this;
      var route={
        name: "详细指标分析",
        params: {
          value_fromYear: self.form.value_fromYear,
          value_fromQuarter: self.form.value_fromQuarter,
          value_toYear: self.form.value_fromYear,
          value_toQuarter: self.form.value_fromQuarter,
          value_orgPartId: self.form.value_orgPartId,
          value_yesno: self.form.value_yesno,
          value_BedNumberScope: self.form.value_BedNumberScope,
          value_areaType: self.form.value_areaType,
          value_medinstCategory: self.form.value_medinstCategory,
          value_orgProp: self.form.value_orgProp,
          value_orgGrade: self.form.value_orgGrade,
          value_orgAttachType: self.form.value_orgAttachType,
          value_clinicaledutype: self.form.value_clinicaledutype,
          value_indexId: row.idxId,
          value_orgId: self.form.value_orgId,
          value_reportTemplate:self.form.value_reportTemplate,
          value_idxName:row.idxName
        },
         meta: { forceReload: true }
      };
      PageUtil.goto(self, route);
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableDatas();
    },

    getSelfCity() {
      this.form.value_areaType = 1;
      this.tabCitys.selfCityType = "primary";
      this.tabCitys.selfProvType = "";
    },

    getSelfProvince() {
      this.form.value_areaType = 0;
      this.tabCitys.selfCityType = "";
      this.tabCitys.selfProvType = "primary";
    },

    pushWhole() {
      var self = this;

      self.tabs.whole = "primary";
      self.tabs.ICU = "";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 1;
      self.getIndexItems();
    },

    pushWard() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "";
      self.tabs.ward = "primary";
      self.form.value_reportTemplate = 2;
      self.getIndexItems();
    },

    pushICU() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "primary";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 3;
      self.getIndexItems();
    },

    changeOrgpart:function(labels) {
      var self=this;

      if(labels.length<=0)
      return;

      var selectId = labels[labels.length-1];
      self.form.value_reportTemplate = formConfig.value_orgPartId[selectId].templateId;  
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableDatas();
    },
    handleOpen(key, keyPath) {
      //console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      //console.log(key, keyPath);
    },
    handleSelect(key, keyPath) {
      //console.log(key);
      //console.log(keyPath);
      //console.log(this.form.menu_map[key]["dictLabel"]);
      // document.getElementById("headName").innerHTML =
      //   '<i class="el-icon-menu"></i>' + this.form.menu_map[key]["dictLabel"];
      // +
      // "--" +
      // this.form.value_hospital;
      this.form.value_indexId = this.form.menu_map[key]["id"];
      this.form.value_groupIdx = this.form.menu_map[key]["partsGroup"];
      if (this.form.value_groupIdx == null) {
        this.form.groupVisible = false;
      } else {
        this.form.groupVisible = true;
      }
    },

    getArea: function() {
      var self = this;
      if (self.form.value_area.length == 0) return;

      if (self.form.value_areaType == 1) {
        if (self.form.value_area.length >= 2)
          //return formConfig.value_areaId[self.form.value_area[1]].treeNames;
          return StringUtil.getTreeNames(formConfig.value_areaId,self.form.value_area[1]);
      }

      //return formConfig.value_areaId[self.form.value_area[0]].treeNames;
      return StringUtil.getTreeNames(formConfig.value_areaId,self.form.value_area[0])
    },
    getTableDatas: function() {
      var self = this;
      self.tableAverMedData = [];
      var templateId = self.form.value_reportTemplate;
      for (var idx in self.form.menu_map) {
        if (!self.form.menu_map[idx]["isTreeLeaf"]) continue;
        if(templateId == 0) continue;
        if(self.form.menu_map[idx]["templateIds"].indexOf(templateId)<0)
        continue;

        self.getAverMedianData(idx, self.form.menu_map[idx]["dictLabel"]);
      }
    },
    //请求季度统计值
    getAverMedianData: function(idxId, idxName) {
      var self = this;
      if (idxId == null) {
        self.$alert("未选择相应指标!请选择指标后，再进行查询!");
        return;
      }

      self.loading = true;
      //地区选择
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.AverMedianDataAction,
        data: {
          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_fromYear,
          toQuarter: self.form.value_fromQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          nationUploadId: self.form.value_yesno,
          bedNumberScopeId: self.form.value_BedNumberScope,
          areaType: self.form.value_areaType,
          medinstCategory: self.form.value_medinstCategory,
          orgProp: self.form.value_orgProp,
          orgGrade: self.form.value_orgGrade,
          orgAttachType: self.form.value_orgAttachType,
          clinicaledutype: self.form.value_clinicaledutype,
          indexId: idxId,
          id: StringUtil.getLastNode(self.form.value_orgId),
          city: self.getArea()
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.averMedianData = res;
          self.total = res.count;
          self.getTableData(idxId, idxName);
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);

      if (self.form.menu_group.hasOwnProperty(self.form.value_groupIdx)) {
        //self.form.groupVisible = true;
        //self.getGroupPercentData();
      } else self.form.groupVisible = false;
    },
    getTableData(idxId, idxName) {
      var self = this;
      var len = self.averMedianData.listListEchartsData.length;
      var res = self.tableAverMedData;
      for (var i = 0; i < len; i++) {
        var objLen = self.averMedianData.listListEchartsData[i].length;

        res.push({
          idxName: idxName,
          idxId: idxId,
          timeStr: self.averMedianData.listListEchartsData[i][0],
          idxVal: self.averMedianData.listListEchartsData[i][1],
          departCnt: self.averMedianData.listListEchartsData[i][2],
          minVal: self.averMedianData.listListEchartsData[i][3],
          quantile10: self.averMedianData.listListEchartsData[i][4],
          quantile25: self.averMedianData.listListEchartsData[i][5],
          quantile50: self.averMedianData.listListEchartsData[i][6],
          quantile75: self.averMedianData.listListEchartsData[i][7],
          quantile90: self.averMedianData.listListEchartsData[i][8],
          maxVal: self.averMedianData.listListEchartsData[i][9]
        });

        //res.push(tmp);
      }
      //console.log("lftest+++-->" + self.tableAverMedData);
    },
    /**
     * 用户权限: 管理员|单位人员
     */
    setupUserPermission: function() {
      var self = this;

      var userInfo = LoginHelper.logincheck(),
        usercode = userInfo ? userInfo.usercode : null;
      //权限, 临时
      //self.user.canStat = usercode && /^(system|bak[0-9]+)$/.test(usercode);
      //self.user.canEdit = usercode && /^(cdr[0-9]+)$/.test(usercode);
      var ajaxOpts = {
        async: false,
        url: global.cmis.domain + "/carePlatform/index/checkRights",
        type: "post",
        dataType: "json",
        data: { code: usercode },
        success: function(res) {
          if (res) {
            self.user.canStat = !!res.canStat;
            self.user.canEdit = !!res.canEdit;
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    }
  }
};
</script>

