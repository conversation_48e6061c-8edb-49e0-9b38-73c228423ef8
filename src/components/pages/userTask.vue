<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="6" :md="4" class="pane-head-label">
              <i class="el-icon-menu"></i>
              我的任务
            </el-col>
            <el-col :span="14" :md="16">
              &nbsp;
            </el-col>
            <el-col :span="4">

              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div
          id="searchFormPane"
          class="search-form-pane"
          :style="{display: form.visible? '' : 'none'}"
        >
          <el-form :inline="true" class="search-form-inline">
            <el-row class="form-row">
              <el-col :span="21">
                &nbsp;
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <div class="dataTableContainer">
            <el-table
              :data="tableData"
              class="tb-edit"
              style="width: 100%"
              highlight-current-row
              stripe
              border
              size="mini"
              v-loading="loading"
              :height="dataTableHeight"
            >
              <el-table-column type="index" width="50"/>
              <el-table-column label="单位名称" prop="orgName" width="160"/>
              <el-table-column label="科室病区" prop="orgPartName" width="160"/>
              <el-table-column label="数据时间" prop="fromQuarterName" width="160"/>
<!--              <el-table-column label="审核流程" width="230">
                <template slot-scope="scope">
                  <el-steps simple :active="scope.row.lastCheckNodeId" finish-status="success">
                    <el-step title="提交"></el-step>
                    <el-step title="院内审核"></el-step>
                    <el-step title="省级审核"></el-step>
                  </el-steps>
                </template>
              </el-table-column>-->
              <el-table-column label="提交状态" prop="execStatusName" width="96"/>
              <el-table-column label="审核状态" prop="lastCheckName" width="96"/>
              <el-table-column label="审核意见" prop="lastCheckFeedback"/>
              <el-table-column label="操作" width="160">
                <template slot-scope="scope">
                  <a href="###" @click="handleEdit('view', scope.row)">查看</a>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">

div.el-steps--simple {
  background-color: transparent;
  line-height: 1.1;
  padding: 0;
}
div.el-step.is-simple {
  flex-basis: auto !important;
  margin-right: 12px !important;
}
div.el-step.is-simple .el-step__head {
  padding-right: 2px;
  font-weight: normal;
}
div.el-step.is-simple .el-step__title {
  font-size: 12px;
  line-height: 1.1;
  font-weight: normal;
}
div.el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: unset;
  word-break: keep-all;
}
div.el-step.is-simple .el-step__arrow {
  margin-left: 4px;
}
</style>

<script>
import global from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";

export default {
  name: "page_userTask",
  
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,

      dataTableHeight: "100%",

      tableDataAction: global.cmis.domain + "/carePlatform/reportResult/findList",

      form: {
        visible: true
      }
    };
  },

  mounted: function() {
    var self = this;

    self.layoutPage();

    self.handleCurrentChange();
  },

  methods: {
    handleEdit(mode, row) {

      this.$router.push({
        name: "单位管理",
        path: "/userOrganization",
        params: {

          taskId: row["id"]
        }
      });
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    getTableData: function() {
      var self = this;

      self.loading = true;

      //请求数据
      var ajaxOpts = {
        type: "post",
        url: self.tableDataAction,
        data: {
          pageNo: self.currentPage,
          pageSize: self.pageSize,

          ["report.jobType"]: 3,

        },
        dataType: "json",
        success: function(res) {
          self.loading = false;

          self.tableData = res.list;
          self.total = res.count;
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    onSearch: function() {
      this.getTableData();
    },

    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    }
  }
};
</script>

