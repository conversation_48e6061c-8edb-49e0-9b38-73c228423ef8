<template>
  <div>
  <el-container>
    <el-main class="data-body">
	  <div v-if="!isRegisterMode">
        <el-row class="pane-head">
          <el-col :span="6" class="pane-head-label"><i class="el-icon-menu"></i>&nbsp;单位管理</el-col>
          
          <el-col :span="14">
            <div class="step-bar-pane">
              <div class="mini-step-bar" v-if="approveForm.enableApprove">
                <el-steps simple :active="form.checkFlow? form.checkFlow.lastNodeId : 0"
                  finish-status="success">
                  <el-step title="提交"></el-step>
                  <el-step title="院内审核" :status="flowStepStatus(2, form.checkFlow)"></el-step>
                  <el-step title="省级审核" :status="flowStepStatus(3, form.checkFlow)"></el-step>
                </el-steps>
              </div>
              <span v-if="!approveForm.enableApprove">&nbsp;</span>
            </div>
          </el-col>
          
          <el-col :span="4">
            <el-button type="primary" @click="prepareEdit" size="mini" v-if="form.enableEdit && !form.underEdit">修改</el-button>
            <el-button type="primary" @click="confirmToDo('save')" size="mini" v-if="form.underEdit">保存</el-button>
            <el-button type="primary" @click="confirmToDo('submit')" size="mini" v-if="form.underEdit">提交审核</el-button>
            <span @click="prepareApprove" class="el-button el-button--primary el-button--mini" v-if="approveForm.enableApprove">审核</span>
          </el-col>
        </el-row>
	  </div>
	  <div class="content-pane" style="margin:16px auto 0;width:680px;">
      <el-form ref="mainForm" :model="form" label-width="140px" :disabled="!form.enableEdit" size="mini">
		    <div class="hide">
			    <el-input type="hidden" v-model="form.id"></el-input>
		    </div>

        <div class="form-items-wrap">

          <el-form-item label=" " v-if="(form.underEdit || approveForm.enableApprove)">
            <el-col :span="11">
              修改前值
            </el-col>
            <el-col :span="12" :offset="1">
              修改后值
            </el-col>
          </el-form-item>
            
          <el-form-item label="机构名称">
            <el-col :span="24">
              <el-input v-model="form.items[0].valueBefore" disabled></el-input>
            </el-col>
          </el-form-item>
            
          <el-form-item label="机构代码">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-input v-model="form.items[1].valueBefore" disabled></el-input>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-input v-model="form.items[1].valueAfter"></el-input>
            </el-col>
          </el-form-item>

          <el-form-item label="机构定级">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[2].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_orgGrade"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[2].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_orgGrade"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="机构定等">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[3].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_orgClass"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[3].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_orgClass"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="机构类型">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[4].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_medinstCategory"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[4].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_medinstCategory"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="所有制性质">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[5].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_orgProp"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[5].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_orgProp"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="行政区划">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <div v-if="form.combo_area_visibility">
              <el-cascader
                v-model="form.items[6].valueBefore"
                disabled
                :show-all-levels="false"
                collapse-tags
                :props="areaCascaderProps(form.items[6].valueBefore)"
                size="mini" ref="combo_area_before"
              ></el-cascader>
              </div>
              <span v-if="!form.combo_area_visibility">&nbsp;</span>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-cascader
                v-model="form.items[6].valueAfter"
                
                :show-all-levels="false"
                collapse-tags
                :props="areaCascaderProps(form.items[6].valueAfter)"
                size="mini"
              ></el-cascader>  
            </el-col>
          </el-form-item>

          <el-form-item label="临床教学基地类型">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[7].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_clinicaledutype"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[7].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_clinicaledutype"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="编制床位数">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-input v-model="form.items[8].valueBefore" disabled></el-input>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-input v-model="form.items[8].valueAfter"></el-input>
            </el-col>
          </el-form-item>

          <el-form-item label="注册护士数">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-input v-model="form.items[9].valueBefore" disabled></el-input>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-input v-model="form.items[9].valueAfter"></el-input>
            </el-col>
          </el-form-item>

          <el-form-item label="隶属类型">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[10].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_orgAttachType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[10].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_orgAttachType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="是否上报国家">
            <el-col :span="(form.underEdit || approveForm.enableApprove)? 11 : 24">
              <el-select v-model="form.items[11].valueBefore" placeholder="全部" size="mini" 
               disabled>
                <el-option
                  v-for="item in form.combo_yesno"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
            <el-col :span="12" :offset="1" v-if="(form.underEdit || approveForm.enableApprove)">
              <el-select v-model="form.items[11].valueAfter" placeholder="全部" size="mini">
                <el-option
                  v-for="item in form.combo_yesno"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item label="审核通过时间">
            <el-col :span="24">
              <el-input v-model="form.items[12].valueBefore" disabled></el-input>
            </el-col>
          </el-form-item>


          <el-form-item label="承诺书">
            <div>支持文件类型: <span v-for="type in form.undertakingAcceptedFileTypes" :key="type">, {{type}}</span></div>
            <el-upload
              size="mini"
              :action="form.uploadAction"
              :show-file-list="false"
              :on-success="onUploadUndertaking"
              :before-upload="beforeUploadUndertaking"
              :disabled="!(form.underEdit || approveForm.enableApprove)"
              >
              <el-button slot="trigger" size="mini" type="primary" :disabled="!(form.underEdit || approveForm.enableApprove)">{{form.items[13].valueBefore? "重新上传" : "点击上传"}}</el-button>

            <span v-if="!!efficientUndertaking(form.items[13])" @click="downloadUndertaking" :style="{'color':'#3c8dbc',cursor:'pointer','font-family':'Source Sans Pro,Helvetica Neue,Helvetica,Arial,sans-serif' }">
            {{efficientUndertaking(form.items[13])}}
            </span>
            </el-upload>
          </el-form-item>

        </div>
        
      </el-form>        

      <div style="margin-top:32px;" v-if="!isRegisterMode">
        <companyManager ref="compMana" :headerTitle="'单位管理员'" editMode="nested" />
      </div>
	  </div>
	
	</el-main>
  </el-container>  

    <el-dialog
      title="数据审核"
	  :visible.sync="approveForm.visible"
      slot="footer"
      class="checkFeedbackDialog"
	  :close-on-click-modal="false"
    >
      <el-form :model="approveForm" :disabled="!approveForm.underApprove">
        <el-form-item label="审核状态" size="mini">
					<el-radio-group v-model="approveForm.checkState" v-on:change="changeState">
			<el-radio              
			  v-for="item in approveForm.combo_checkState"
              :key="item.value"
              :label="item.value">{{item.label}}</el-radio>
					</el-radio-group>	
        </el-form-item>

        <el-form-item label="审核意见"></el-form-item>

        <el-form-item label>
          <!-- DIRECT CHAT PRIMARY -->
          <div class="box box-primary direct-chat direct-chat-primary box-noborder">
            <div class="box-body">
              <!-- Conversations are loaded here -->
              <div id="checkDataPane" class="direct-chat-messages">
                <div class="direct-chat-msg" v-for="item in approveForm.checkData" :key="item.id">
                  <div class="direct-chat-info clearfix">
                    <span
                      class="direct-chat-name pull-left"
                    >{{item.entered? item.entered.dictLabel : ''}}({{item.enteredAtName}})</span>
                    <span class="direct-chat-timestamp pull-right">{{item.enteredOn}}</span>
                  </div>
                  <!-- /.direct-chat-info -->
                  <img class="direct-chat-img" :src="approveForm.user_avatar" alt>
                  <!-- /.direct-chat-img -->
                  <div class="direct-chat-text direct-chat-text-mix">
                    <div class="direct-chat-text-tag" v-if="1 < item.nodeId">{{item.checkStateName}}</div>
                    <div class="direct-chat-text-body">{{item.checkFeedback}}</div>
                  </div>
                  <!-- /.direct-chat-text -->
                </div>
                <!-- /.direct-chat-msg -->

              </div>
              <!--/.direct-chat-messages-->
            </div>
            <!-- /.box-body -->
            <div class="box-footer box-noborder">
              <el-row>
                <el-col :span="20" class="align-center">
                  <el-input
                    size="mini"
                    v-model="approveForm.checkFeedback"
                    placeholder="我的意见"
                    class="textfield-checkFeedBack"
                  ></el-input>
                </el-col>
                <el-col :span="4" class="align-center">
                  <el-button size="mini" type="primary" @click="confirmToDo('approve')">提交</el-button>
                </el-col>
              </el-row>
            </div>
            <!-- /.box-footer-->
          </div>
          <!--/.direct-chat -->
        </el-form-item>
      </el-form>
      
    </el-dialog>

  </div>
</template>

<style type="text/css">
div.form-grid th{
	background-color:transparent;
}
div.form-grid th.is-leaf, div.form-grid td{
	border-bottom-width: 0;
}
div.form-grid .form-grid-column-label{
	font-size: 14px;
	font-weight: bold;
}
div.form-grid:before{
	height:0;
}

.lft-col{
	margin:0 auto;
	width:480px;
	text-align:center;
}
div.lft-col .el-form-item__content{
	margin-left:0 !important;
}
.box-noborder{
	border-top-width: 0;
	border-width: 0;
}

div.step-bar-pane{
	text-align:right;
}
div.mini-step-bar{
	width: 220px;
	margin: 6px 8px 0px auto;
}

div.form-items-wrap label{
  margin-bottom:0;
}

</style>

<script>

  import me from "@/assets/js/pages/userOrganization";

  export default me;
</script>