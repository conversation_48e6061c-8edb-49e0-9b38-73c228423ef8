<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="16" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;离线文件上传和监控
            </el-col>
            <el-col :span="8">
              <!--<el-upload
                class="upload-box"
                :action="uploadAction"
                multiple
                :limit="1"
                :show-file-list="false"
                :data="uploadData"
                :on-success="handleSuccessUpload"
                :on-error="handleErrorUpload"
                :on-progress="handleProgressUpload"
                :before-upload="beforeUploadFile"
              >
                <el-button size="mini" type="primary">上传</el-button>
              </el-upload>
              <el-button size="mini">下载模板</el-button>-->
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline" v-if="true">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="数据时间">
                  <el-date-picker
                    type="daterange"
                    v-model="form.searchDataTime"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item label="上送时间">
                  <el-date-picker
                    type="date"
                    v-model="form.searchUploadTime"
                    placeholder="选择日期"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item>
                  <el-button type="primary" @click="onSearch" size="mini">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <div class="dataTableContainer">
            <el-table
              :data="tableData"
              class="tb-edit"
              style="width: 100%"
              highlight-current-row
              stripe
              border
              size="mini"
              v-loading="loading"
              :height="dataTableHeight"
            >
              <el-table-column type="index" width="50" />
              <el-table-column label="上报单位" prop="enteredAtName" width="160" />
              <el-table-column label="采集范围" prop="orgPartName" width="96" />
              <el-table-column label="数据时间" prop="fromTime" width="160" />
              <el-table-column label="上传文件" prop="upName" width="160" />
              <!--<el-table-column label="处理状态" prop="loadState" width="96"/>-->
              <el-table-column label="上报时间" prop="enteredOn" width="160" />
              <el-table-column label="上传记录数" prop="submitRecordNum" width="96" />
              <el-table-column label="入库失败数" prop="failureRecordNum" width="96" />
              <el-table-column label="入库成功数" prop="successRecordNum" width="96" />
              <el-table-column label="数据异常数" prop="anomalyRecordNum" width="96" />
              <el-table-column label="上报人" prop="enteredByName" width="160" />
              <!--<el-table-column label="接口方式" prop="interfaceMode" width="96"/>-->
              <el-table-column label="操作" width="160">
                <template slot-scope="scope">
                  <!-- <el-button size="mini" @click="readIndex(scope.$index, scope.row)">查看</el-button>
                  <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">重传</el-button>-->
                  <!-- <router-link :to="{name:'m001_002', params:{id:scope.row['id']}}">查看</router-link> -->
                  <!--<a href="###" @click="handleView(scope.$index, scope.row)">查看</a>
                  <a href="###" @click="handleDelete(scope.$index, scope.row)">重传</a>-->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style type="text/css">
.data-body {
  padding: 0;
}
.pane-head-label {
  line-height: 30px;
}
.search-form-inline .el-form-item {
  margin-bottom: 0px;
}

.upload-box {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__input {
  display: none !important;
}
</style>

<script>
import global from "@/config/global-config";
//import $ from 'jquery'
import DateUtil from "@/utils/DateUtil";
import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";

export default {
  name: "page_uploadLogList",

  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      dataTableHeight: "100%",
      loading: false,

      form: {
        visible: PageUtil.searchFormVisibility(),
        searchUploadTime: "",
        searchDataTime: []
      },
      //上传文件时，携带的参数
      uploadData: {
        reportResultId: "",
        enteredAt: "",
        orgPartId: "",
        fromTime: "",
        dataPeriod: "",
        enteredOn: "",
        enteredBy: "",
        itemTypeName: "",
        statisticId: ""
      },
      //, uploadAction: 'http://*************:8081/fileupload'
      uploadAction: global.platform.indexUploadAction,
      //, uploadAction: 'http://127.0.0.1:8081/fileupload'
      //, tableDataAction: 'http://127.0.0.1:8880/rh-cmis/carePlatform/index/getUploadLogsList',
      tableDataAction:
        global.cmis.domain + "/carePlatform/index/getUploadLogsList",

      loadingMask: null
    };
  },

  created: function() {
    this.layoutPage();

    this.getTableData();
  },

  methods: {
    beforeUploadFile(file) {
      // if (size > 10) {
      //   this.$confirm("文件大小不得超过500M", {
      //     confirmButtonText: "确定"
      //   });
      //   return false;
      // }

      this.uploadData.reportResultId = "99";
      this.uploadData.enteredAt = "4";
      this.uploadData.orgPartId = "1";
      this.uploadData.fromTime = "2020-01-01 00:00:00";
      this.uploadData.dataPeriod = "4";
      this.uploadData.enteredOn = "2020-01-01 15:00:00";
      this.uploadData.enteredBy = "1";
      this.uploadData.itemTypeName = "原始数据";
      this.uploadData.statisticId = "1";
    },
    readIndex(index, row) {},
    handleView(index, row) {
      console.log(row);
      var route = {
        name: "数据填报",
        path: "/indexItemsPanels",
        params: {
          id: row["id"],
          fromTime: DateUtil.dateToStr(
            DateUtil.strToDate(row["fromTime"]),
            "yyyy-MM-dd"
          ),
          toTime: DateUtil.dateToStr(
            DateUtil.strToDate(row["fromTime"]),
            "yyyy-MM-dd"
          ),
          orgPartId: row["orgPartId"],
          orgId: row["organizationId"]
        },
        meta: { forceReload: true }
      };
      PageUtil.goto(this, route);
    },
    handleDelete(index, row) {},
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },
    getTableData: function() {
      var self = this;
      //数据时间
      var searchDataTime = this.form.searchDataTime,
        fromTime,
        toTime;
      if (searchDataTime && searchDataTime.length) {
        fromTime = DateUtil.dateToStr(searchDataTime[0]);
        toTime = DateUtil.dateToStr(searchDataTime[1]);
      }
      //上传时间
      var enteredOn;
      if (this.form.searchUploadTime) {
        enteredOn = DateUtil.dateToStr(this.form.searchUploadTime);
      }

      self.loading = true;
      //请求数据
      AjaxUtil.send({
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        type: "post",
        url: self.tableDataAction,
        data: {
          pageNo: self.currentPage,
          pageSize: self.pageSize,

          enteredOn: enteredOn,
          fromTime: fromTime,
          toTime: toTime
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;

          self.tableData = res.list;
          self.total = res.count;
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      });
    },
    onSearch: function() {
      this.getTableData();
    },
    onChangeOrg: function(data) {
      this.getTableData();
    },

    handleSuccessUpload: function(response, file, fileList) {
      var self = this;
      self.loadingMask.close();

      var msg = "提交成功";
      // "上传完成(总数=" +
      // response.totalNum +
      // ", 成功=" +
      // (response.successNum | response.sucessNum) +
      // ", 失败=" +
      // response.failNum +
      // ")";
      self.$alert(msg, "上传结果", {
        callback: function() {
          //if (response["successNum"] > 0) {
          self.getTableData();
          //}
        }
      });
    },
    handleErrorUpload: function(err, file, fileList) {
      var self = this;
      self.loadingMask.close();

      self.$alert(err);
    },
    handleProgressUpload: function(event, file, fileList) {
      var self = this;

      self.loadingMask = this.$loading({
        lock: true,
        text: "正在处理....",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
    },

    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    }
  }
};
</script>