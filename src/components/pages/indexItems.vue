<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div v-if="showPaneHead">
          <el-row class="pane-head">
            <el-col :span="3" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;数据填报
            </el-col>
            <el-col :span="13">
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.singleTask"
                @click="pushSingleTask"
              >国家模板文件导入
              </el-button>
              <el-button
                size="mini"
                class="tab-button"
                :type="tabs.multipleTask"
                @click="pushMultipleTask"
              >国家导出数据批量导入
              </el-button>
            </el-col>
<!--            <el-col :span="6">
              <el-button size="mini" @click="downLoad">下载模板</el-button>
              <el-button size="mini" @click="swapFormVisibility">{{ form.visible ? '隐藏' : '展开' }}查询条件
              </el-button>
            </el-col>-->
          </el-row>
        </div>
        <div :style="{display: form.visible? '' : 'none'}">
          <el-form
            ref="mainForm"
            :inline="true"
            class="search-form-inline"
            :model="form"
            :rules="formRules"
          >
            <el-input type="hidden" v-model="form.value_id" style="display:none"></el-input>
            <el-row class="form-row">
<!--              <el-col :span="18">
                <el-form-item label="任务类型" class="form-h-item" prop="jobType">
                  <el-select
                    v-model="form.value_jobType"
                    placeholder="请设置"
                    size="mini"
                    @change="selectJobType"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_jobType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                &lt;!&ndash; <el-form-item label="单位" class="form-h-item">
                  <el-select
                    v-model="form.value_orgId"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_orgId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>&ndash;&gt;
                <el-form-item label="单位" class="form-h-item" v-if="!isMultipleTask">
                  <el-cascader
                    v-model="form.value_orgId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>
                &lt;!&ndash; <el-form-item label="病区" class="form-h-item">
                  <el-select
                    v-model="form.value_orgPartId"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_orgPartId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>&ndash;&gt;
                <el-form-item label="科室病区" class="form-h-item" v-if="!isMultipleTask">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="任务时间" v-if="form.value_jobPeriod != '4' && !isMultipleTask">
                  <el-date-picker
                    type="month"
                    v-model="form.value_dataFromTime"
                    placeholder="开始日期"
                    size="mini"
                    :disabled="formDisabled"
                  ></el-date-picker>
                </el-form-item>

                <el-form-item
                  label="任务时间"
                  class="form-h-item"
                  v-if="form.value_jobPeriod == '4'  && !isMultipleTask"
                >
                  <el-select
                    v-model="form.value_fromTime"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                &lt;!&ndash; <el-form-item label="季度" class="form-h-item" prop="year" v-if="!isMultipleTask">
                  <el-select
                    v-model="form.value_year"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_year"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label class="form-h-item" prop="quarter">
                  <el-select
                    v-model="form.value_quarter"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                    v-if="!isMultipleTask"
                  >
                    <el-option
                      v-for="item in form.combo_quarter"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>&ndash;&gt;
              </el-col>-->
              <el-col :span="6" style="margin-left: 10px">
                <el-form-item>
                  <el-button type="primary" @click="onSearch" size="mini">刷新</el-button>

<!--                  <el-button
                    type="primary"
                    @click="prepareApprove"
                    size="mini"
                    v-if="underApprove"
                  >审核
                  </el-button>-->

                  <el-button
                    type="primary"
                    @click="confirmToSaveData()"
                    size="mini"
                    v-if="enableEdit"
                  >保存
                  </el-button>

<!--                  <el-button
                    type="primary"
                    @click="confirmToSaveData('submit')"
                    size="mini"
                    v-if="enableEdit"
                  >提交审核
                  </el-button>-->

                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane" style="width:99.4%;min-width:680px">
          <el-form ref="editForm" inline :model="table">
            <el-table
              :data="table.data"
              :span-method="commentSpanMethod"
              class="tb-indexedit table-form-validate"
              highlight-current-row
              stripe
              border
              size="medium"
              v-loading="loading"
              :row-class-name="tableRowClassName"
              @cell-mouse-enter="handleRowEnter"
              @cell-mouse-leave="handleRowLeave"
              @cell-click="handleOnClick"
            >
              <el-table-column label="序号" prop="code" width="60"/>
              <el-table-column label="指标" prop="name">
                <template slot-scope="scope">
                  <!-- <el-tooltip :content="scope.row.description" placement="bottom" effect="light"> -->
                  <div>{{ scope.row.name }}</div>
                  <div v-if="!!validator.result[scope.row.calcObjId]">
                    <i :class="'el-icon-' + validator.result[scope.row.calcObjId]['type']"
                       style="color:#dd4b39; font-size:1em;"></i>
                    {{ validator.result[scope.row.calcObjId]["message"] }}
                  </div>
                  <!-- </el-tooltip> -->
                </template>
              </el-table-column>
              <el-table-column label="数值" prop="value" width="120">
                <template slot-scope="scope">
                  <span v-if="!enableEdit || '2' == scope.row.editType">{{ scope.row.value }}</span>
                  <el-input
                    v-if="enableEdit && '2' != scope.row.editType"
                    v-model="scope.row.value"
                    :maxlength="50"
                    size="mini"
                    :class="'cell-textfield-' + (validator.result[scope.row.calcObjId]? validator.result[scope.row.calcObjId]['type'] : 'normal')"
                    @change="triggerAutoCalc(scope.row)"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="注释" prop="comments">
                <template slot-scope="scope">
                  <span v-if="scope.row.comments" style="margin-left: 0px;">
                    <i class="el-icon-question" style="color:#dd4b39; font-size:large">说明:</i>
                    <br/>
                    {{ scope.row.comments }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      title="数据审核"
      :visible.sync="approveForm.visible"
      slot="footer"
      class="checkFeedbackDialog"
      :close-on-click-modal="false"
    >
      <el-form :model="approveForm">
        <el-form-item label="审核状态" size="mini" v-if="enableApprove">
          <el-radio-group v-model="approveForm.value_checkState" v-on:change="changeState">
            <el-radio
              v-for="item in approveForm.combo_checkState"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}
            </el-radio>
          </el-radio-group>
          <!-- <el-select v-model="approveForm.value_checkState" placeholder="未审核" size="mini">
            <el-option
              v-for="item in approveForm.combo_checkState"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>-->
        </el-form-item>

        <el-form-item label="审核意见"></el-form-item>

        <el-form-item label>
          <!-- DIRECT CHAT PRIMARY -->
          <div class="box box-primary direct-chat direct-chat-primary box-noborder">
            <div class="box-body">
              <!-- Conversations are loaded here -->
              <div id="checkDataPane" class="direct-chat-messages">
                <div class="direct-chat-msg" v-for="(item) in checkData" :key="item.id">
                  <div class="direct-chat-info clearfix">
                    <span
                      class="direct-chat-name pull-left"
                    >{{ item.entered ? item.entered.dictLabel : '' }}({{ item.enteredAtName }})</span>
                    <span class="direct-chat-timestamp pull-right">{{ item.enteredOn }}</span>
                  </div>
                  <!-- /.direct-chat-info -->
                  <img class="direct-chat-img" :src="approveForm.user_avatar" alt/>
                  <!-- /.direct-chat-img -->
                  <div class="direct-chat-text direct-chat-text-mix">
                    <div class="direct-chat-text-tag" v-if="1 < item.nodeId">{{ item.checkStateName }}</div>
                    <div class="direct-chat-text-body">{{ item.checkFeedback }}</div>
                  </div>
                  <!-- /.direct-chat-text -->
                </div>
                <!-- /.direct-chat-msg -->

                <div style="display:none !important;">
                  <!-- Message. Default to the left -->
                  <div class="direct-chat-msg">
                    <div class="direct-chat-info clearfix">
                      <span class="direct-chat-name pull-left">上报负责人</span>
                      <span class="direct-chat-timestamp pull-right">2019-04-11 13:24:01</span>
                    </div>
                    <!-- /.direct-chat-info -->
                    <!-- <img class="direct-chat-img" src="/themes/AdminLTE/dist/img/user1-128x128.jpg" alt="message user image"> -->
                    <!-- /.direct-chat-img -->
                    <div class="direct-chat-text">上报指标.</div>
                    <!-- /.direct-chat-text -->
                  </div>
                  <!-- /.direct-chat-msg -->

                  <!-- Message to the right -->
                  <div class="direct-chat-msg right" style="display:none">
                    <div class="direct-chat-info clearfix">
                      <span class="direct-chat-name pull-right">Sarah Bullock</span>
                      <span class="direct-chat-timestamp pull-left">23 Jan 2:05 pm</span>
                    </div>
                    <!-- /.direct-chat-info -->
                    <img
                      class="direct-chat-img"
                      src="/themes/AdminLTE/dist/img/user3-128x128.jpg"
                      alt="message user image"
                    />
                    <!-- /.direct-chat-img -->
                    <div class="direct-chat-text">You better believe it!</div>
                    <!-- /.direct-chat-text -->
                  </div>
                  <!-- /.direct-chat-msg -->
                </div>
              </div>
              <!--/.direct-chat-messages-->
            </div>
            <!-- /.box-body -->
            <div class="box-footer box-noborder" v-if="enableApprove">
              <!-- <div class="input-group">
													<input type="text" name="message" placeholder="审核意见" class="form-control" :model="approveForm.value_checkState">
													<span class="input-group-btn" style="line-height:1;">
														<button type="button" class="btn btn-primary btn-flat">提交</button>
													</span>
              </div>-->
              <el-row>
                <el-col :span="20" class="align-center">
                  <el-input
                    size="mini"
                    v-model="approveForm.value_checkFeedback"
                    placeholder="审核意见"
                    class="textfield-checkFeedBack"
                  ></el-input>
                </el-col>
                <el-col :span="4" class="align-center">
                  <el-button size="mini" type="primary" @click="approve">提交</el-button>
                </el-col>
              </el-row>
            </div>
            <!-- /.box-footer-->
          </div>
          <!--/.direct-chat -->
        </el-form-item>
      </el-form>
      <!-- <div slot="footer" class="dialog-footer">
				<el-button @click="approveForm.visible = false" size="mini">取 消</el-button>
				<el-button type="primary" @click="approve">确 定</el-button>
      </div>-->
    </el-dialog>
  </div>
</template>

<style type="text/css">
div.textfield-checkFeedBack {
  margin: 0 auto;
  text-align: center;
}

div.el-table .table-row-group td,
div.el-table--striped table.el-table__body tr.table-row-group td {
  color: #fff !important;
  background-color: #3a8ee6 !important;
}

div.el-table .table-row-group-gx td,
div.el-table--striped table.el-table__body tr.table-row-group-gx td {
  color: #fff !important;
  background-color: #00974c !important;
}

.tb-indexedit {
  font-size-adjust: inherit;
  font-size: medium;
}

div.upload-box {
  display: inline-block;
  margin-right: 10px;
}

input.el-upload__input {
  display: none !important;
}

div.table-form-validate div.cell {
  overflow: visible;
}

form.el-form--inline div.table-cell-form-item {
  margin: 0 auto;
}

div.cell-textfield-error input.el-input__inner {
  border-color: #F56C6C;
}
</style>

<script>
import model from "@/assets/js/pages/indexItems";

export default model;
</script>
