<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="21" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;病区总览
            </el-col>
            <el-col :span="3">
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>
        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline" :rule="form.rules">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label>
                  <el-button-group>
                    <el-button size="mini" :type="tabs.whole" @click="pushWhole">全院</el-button>
                    <el-button size="mini" :type="tabs.ward" @click="pushWard">病区</el-button>
                    <el-button size="mini" :type="tabs.ICU" @click="pushICU">ICU</el-button>
                  </el-button-group>
                </el-form-item>
                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="partUserFormDisabled"
                  ></el-cascader>
                </el-form-item>

                <!-- <el-form-item label="上报模板" class="form-h-item">
                  <el-select v-model="form.value_reportTemplate" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_reportTemplate"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromTime" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="指标选择" class="form-h-item">
                  <el-select v-model="form.value_indexes" multiple placeholder="全部">
                    <el-option
                      v-for="item in form.combo_indexes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getIndexItems">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="item-view-grid"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            v-loading="loading"
            :height="dataTableHeight" ref="dataTable"
          >
            <el-table-column type="index" width="50" fixed/>
            <template v-for="(col, key, index) in cols">
              <el-table-column
                :type="col.type"
                :prop="col.prop"
                sortable
                :label="col.label"
                :width="col.width || 90"
                v-bind:key="index"
                :fixed="col.fixed"
              >
                <template slot-scope="scope">
                  <el-tag type="primary" v-if="col.type==='sort'">{{ scope.row.type }}</el-tag>
                  <!-- <i :class="{'el-icon-check': 'currentDataIsUpload' == col.prop && scope.row[col.prop]}"></i> -->
                  <i
                    v-if="'currentDataIsUpload' == col.prop"
                    :class="{'el-icon-check': scope.row[col.prop], 'el-icon-close': !scope.row[col.prop]}"
                  ></i>
                  <span v-if="'currentDataIsUpload' != col.prop">{{ scope.row[col.prop] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<style>
div.item-view-grid .el-icon-check {
  color: green;
}
div.item-view-grid .el-icon-close {
  color: red;
}
</style>

<script>
import globalVariables from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import LayoutUtil from "@/utils/PageUtil";
import StringUtil from "@/utils/StringUtil";
import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";
import indexConfig from "@/config/index-config";

export default {
  name: "page_indexWardOverview",
  
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      dataTableHeight: "100%",
      loading: false,
      formDisabled: true,
      partUserFormDisabled: false,
      tableDataAction:
        globalVariables.cmis.domain + "/carePlatform/qcIndex/view",

      tabs: {
        whole: "primary", //全院模版 1
        Ward: "", //病区模板  2
        ICU: "" //ICU模版  3
      },

      form: {
        visible: LayoutUtil.searchFormVisibility(),

        combo_submitState: formConfig.combo_submitState,
        //lzd todo, 默认值设置，提交时赋值，因为 多级选择框的值格式是：2080,2105,2106
        /*combo_orgPartId: (function() {
          var tmp = formConfig.combo_orgPartIdHierarchical.slice(0);
          if (tmp[0].label == "所有") tmp.shift();
          return tmp;
        })(),*/

        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_templateType: formConfig.combo_reportTemplate,

        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),
        combo_indexes: (function() {
          var tmp = [];
          var cfgIdx = indexConfig["idx"];
          for (var idx in cfgIdx) {
            if (cfgIdx[idx]["isTreeLeaf"] == false) continue;
            tmp.push({ label: cfgIdx[idx].dictLabel, value: cfgIdx[idx].id });
          }
          return tmp;
        })(),
        combo_reportTemplate: formConfig.combo_reportTemplate,

        value_fromTime: "",
        value_enteredAt: formConfig.value_user.org_id,
        value_orgId: [formConfig.value_user.org_id], //lzd todo 默认查询下级,数据权限
        value_orgPartId: [""],
        value_indexes: "",

        value_orgName: "",
        value_area: formConfig.value_user.org_area,
        value_reportTemplate: formConfig.value_user.hasOwnProperty("orgPart_id")?formConfig.value_user.orgPart_template_id:1,

        rules: {
          value_fromTime: [{ required: true, message: "请选择开始季度" }]
        }
      },
      cols: [
        {
          label: "病区名称",
          prop: "orgPart",
          type: "normal",
          width: 200 //,
          //fixed: true
        }
      ],
      tableData: [{}]
    };
  },

  mounted: function() {
    var self = this;
    self.layoutPage();

    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    }

    var now = new Date();
    //初始开始, 结束季度
    var from = new Date(DateUtil.dateAdd("q", -1, new Date()));
    self.form.value_fromTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(from),
      "yyyy-MM-dd"
    );

    self.form.value_toTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(now),
      "yyyy-MM-dd"
    );

    //参数
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);
    //
    var k, v;
    for (k in formValues) {
      v = formValues[k];
      if (!v) {
        continue;
      }
      if ("area" == k) {
        v = StringUtil.getIdsById(formConfig.value_areaId, v);
      }
      self.form["value_" + k] = v;
    }
    //省级管理员不指定机构
    if (1 == formConfig.value_user.org_id) {
      self.form["value_orgId"] = [""];
    }
    //
    //this.getIndexItems();
    if(self.form.value_reportTemplate==1)
    {
      self.pushWhole();
    }
    else if(self.form.value_reportTemplate==2)
    {
      self.pushWard();
    }
    else if(self.form.value_reportTemplate==3)
    {
      self.pushICU();
    }
  },

  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getIndexItems();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getIndexItems();
    },

    pushWhole() {
      var self = this;

      self.tabs.whole = "primary";
      self.tabs.ICU = "";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 1;
      self.getIndexItems();
    },

    pushWard() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "";
      self.tabs.ward = "primary";
      self.form.value_reportTemplate = 2;
      self.getIndexItems();
    },

    pushICU() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "primary";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 3;
      self.getIndexItems();
    },


    /**
     * 读取指标
     */
    getIndexItems: function() {
      var self = this;
      //是否有单位档案
      // if (0 >= self.total) {
      //   return;
      // }
      //保留前23列, 单位档案
      var colDocNum = 1;
      var cols = self.cols.slice(0, colDocNum);
      self.cols = cols;

      //self.$alert(self.form.value_toTime);
      //表单验证
      if (!self.form.value_fromTime || !self.form.value_toTime) {
        self.$alert('请选择"开始季度"和"截止季度".');
        return;
      }
      //console.log(self.form);
      //
      var ajaxOptsIdx = {
        url: globalVariables.cmis.domain + "/carePlatform/index/getItemsList",
        type: "post",
        dataType: "json",
        data: {
          "quarterRange.fromTime": self.form.value_fromTime,
          "quarterRange.toTime": self.form.value_fromTime,
          enteredAt: self.form.value_enteredAt,
          orgId: StringUtil.getLastNode(self.form.value_orgId),
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          itemTypeName: "指标数据",
          "orgPart.extend.extendI4": self.form.value_reportTemplate
        },
        complete: function() {
          self.loading = false;
        },
        success: function(res) {
          var tableData = [{}];
          var orgPartId =
            self.form.value_orgPartId[self.form.value_orgPartId.length - 1];
          // var orgPartName = StringUtil.getTreeNames(
          //   formConfig.value_orgPartId,
          //   orgPartId
          // );
          // if(formConfig.value_orgPartId.hasOwnProperty(orgPartId))
          // {
          //    orgPartName = formConfig.value_orgPartId[orgPartId].treeNames;
          // }
          //tableData[0].orgPart = orgPartName;

          var idxItems = res ? res["data"] : null;
          if (!idxItems || !idxItems.length) {
            return;
          }
          var re = /\./;
          var orgPartName='';
          for (var j = 0, itm, prop, iprop; j < idxItems.length; j++) {
            itm = idxItems[j];

            if (!re.test(itm["code"])) {
              //过滤掉code不含'.'的二级标题
              //console.log(itm);
              continue;
            }

            if(itm.hasOwnProperty("orgPartId") && orgPartName=='')
            {
               orgPartId = itm['orgPartId'];
               orgPartName = StringUtil.getTreeNames(
                  formConfig.value_orgPartId,
                  orgPartId
                  );
               tableData[0].orgPart = orgPartName;
            }
            //
            prop = "index" + itm["code"]; //itm["id"];
            iprop = false; //是否有了这个指标列
            for (var j1 = colDocNum; j1 < cols.length; j1++) {
              if ((iprop = prop == cols[j1]["prop"])) {
                break;
              }
            }
            if (!iprop) {
              cols.push({
                label: itm["name"],
                prop: prop,
                type: "normal",
                width: 180
              });
            }
            if (typeof itm["value"] == "undefined") continue;
            tableData[0][prop] = itm["value"];
            // if (!itm["fromTime"]) {
            //   continue;
            // }
            //
            // var dt = DateUtil.quarterLabelOfDate(
            //   DateUtil.strToDate(itm["fromTime"])
            // );
            //
            // for (var k = 0, tr; k < tableData.length; k++) {
            //   tr = tableData[k];
            //   //对比单位, 科室病区, 填报季度
            //   if (
            //     tr["orgId"] == itm["organizationId"] &&
            //     tr["orgPartId"] == itm["orgPartId"]
            //   ) {
            //     if (tr["currentDataQuarter"] == dt) {
            //       tr["currentDataIsUpload"] = 1;
            //       tr[prop] = itm["value"];
            //     }
            //   }
            // }
          }
          self.cols = cols;
          self.tableData = tableData;
          
          setTimeout(function() {
            self.$refs.dataTable && self.$refs.dataTable.doLayout();
          }, 0);
        }
      };
      //
      self.loading = true;
      //
      AjaxUtil.send(ajaxOptsIdx);
    },

    onSearch: function() {
      this.getTableData();
    },

    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = LayoutUtil.dataTableHeight();
      }, 0);
    }
  }
};
</script>

