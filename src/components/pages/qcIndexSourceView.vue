<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader" v-if="showPaneHead">
          <el-row class="pane-head">
            <el-col :span="21" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;原始数据查询
            </el-col>
            <el-col :span="3">
              <el-button size="mini" @click="swapFormVisibility">{{ form.visible ? '隐藏' : '展开' }}查询条件
              </el-button>
            </el-col>
          </el-row>
        </div>
        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline" :rule="form.rules">
            <el-row class="form-row">
              <el-col :span="18">
<!--                <el-form-item>
                  <el-button-group>
                    <el-button size="mini" :type="tabs.whole" @click="pushWhole">全院</el-button>
                    <el-button size="mini" :type="tabs.ward" @click="pushWard">病区</el-button>
                    <el-button size="mini" :type="tabs.ICU" @click="pushICU">ICU</el-button>
                  </el-button-group>
                </el-form-item>-->

                <!--                <el-form-item label="任务类型" class="form-h-item" prop="jobType">
                                  <el-select
                                    v-model="form.value_jobType"
                                    placeholder="请设置"
                                    size="mini"
                                    @change="selectJobType"
                                  >
                                    <el-option
                                      v-for="item in form.combo_jobType"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>-->

                <!--                <el-form-item label="地区" class="form-h-item">
                                  <el-cascader
                                    v-model="form.value_area"
                                    placeholder="试试搜索"
                                    size="mini"
                                    :options="form.combo_area"
                                    filterable
                                    change-on-select
                                    :show-all-levels="false"
                                    :disabled="formDisabled"
                                  ></el-cascader>
                                </el-form-item>

                                <el-form-item label="单位" class="form-h-item">
                                  <el-cascader
                                    v-model="form.value_orgId"
                                    placeholder="试试搜索"
                                    size="mini"
                                    :options="form.combo_orgId"
                                    filterable
                                    change-on-select
                                    :show-all-levels="false"
                                    :disabled="formDisabled"
                                  ></el-cascader>
                                </el-form-item>-->

                <!-- <el-form-item label="单位名称" class="form-h-item">
                  <el-input v-model="form.value_orgName" size="mini"></el-input>
                </el-form-item>-->
                <!-- <el-form-item label="科室病区" class="form-h-item">
                  <el-select v-model="form.value_orgPartId" placeholder="全院" size="mini">
                    <el-option
                      v-for="item in form.combo_orgPartId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>-->
                <!--                <el-form-item label="科室病区" class="form-h-item">
                                  <el-cascader
                                    v-model="form.value_orgPartId"
                                    placeholder="试试搜索"
                                    size="mini"
                                    :options="form.combo_orgPartId"
                                    filterable
                                    change-on-select
                                    :show-all-levels="false"
                                    :disabled="partUserFormDisabled"
                                  ></el-cascader>
                                </el-form-item>-->

<!--                <el-form-item label="数据日期" v-if="form.value_jobPeriod != '4'">
                  <el-date-picker
                    type="monthrange"
                    v-model="form.value_searchDataTime"
                    range-separator="——"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    size="mini"
                    align="right"
                    unlink-panels
                    :picker-options="pickerOptions"
                    format="yyyy 年 MM 月"
                  ></el-date-picker>
                </el-form-item>-->

                <el-form-item label="开始季度" label-width="6.2em" v-if="form.value_jobPeriod === 4">
                  <el-select v-model="form.value_fromTime" placeholder="全部" size="medium" clearable>
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度"  v-if="form.value_jobPeriod === 4">
                  <el-select v-model="form.value_toTime" placeholder="全部" size="medium" clearable>
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <!--                <el-form-item label="上报国家" class="form-h-item">
                                  <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                                    <el-option
                                      v-for="item in form.combo_yesno"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>-->

                <!--                <el-form-item label="提交状态" class="form-h-item">
                                  <el-select v-model="form.value_submitState" placeholder="全部" size="mini">
                                    <el-option
                                      v-for="item in form.combo_submitState"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>

                                <el-form-item label="终审状态" class="form-h-item">
                                  <el-select v-model="form.value_checkState" placeholder="全部" size="mini">
                                    <el-option
                                      v-for="item in form.combo_checkState"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>-->

                <!--                <el-form-item label="单位定级" class="form-h-item">
                                  <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                                    <el-option
                                      v-for="item in form.combo_orgGrade"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value"
                                    ></el-option>
                                  </el-select>
                                </el-form-item>-->
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button type="success" size="mini" @click="showUploadDialog">上传</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 移植日期筛选器、姓名筛选器和住院号筛选器在同一行 -->
            <el-row class="form-row">
              <el-col :span="7">
                <el-form-item label="移植日期" label-width="6.2em">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <el-date-picker
                      type="date"
                      v-model="form.value_surgicalFromDate"
                      placeholder="开始日期"
                      size="medium"
                      clearable
                      editable
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 140px;"
                    ></el-date-picker>
                    <span style="color: #606266;">至</span>
                    <el-date-picker
                      type="date"
                      v-model="form.value_surgicalToDate"
                      placeholder="结束日期"
                      size="medium"
                      clearable
                      editable
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 140px;"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="姓名">
                  <el-input
                    v-model="form.value_patientName"
                    placeholder=""
                    size="medium"
                    clearable
                    style="width: 110px"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="住院号">
                  <el-input
                    v-model="form.value_mrn"
                    size="medium"
                    clearable
                    style="width: 110px"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="性别">
                  <el-select v-model="form.value_gender" placeholder="全部" size="medium" style="width: 120px">
                    <el-option
                      v-for="item in form.combo_gender"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="民族">
                  <el-select v-model="form.value_ethnicGroup" placeholder="全部" size="medium" style="width: 120px">
                    <el-option
                      v-for="item in form.combo_ethnicGroup"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="出生日期" label-width="6.2em">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <el-date-picker
                      type="date"
                      v-model="form.value_birthFromDate"
                      placeholder="开始日期"
                      size="medium"
                      clearable
                      editable
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 140px;"
                    ></el-date-picker>
                    <span style="color: #606266;">至</span>
                    <el-date-picker
                      type="date"
                      v-model="form.value_birthToDate"
                      placeholder="结束日期"
                      size="medium"
                      clearable
                      editable
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 140px;"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="item-view-grid"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            v-loading="loading"
            :height="dataTableHeight"
            ref="dataTable"
          >
            <el-table-column type="index" width="50" fixed/>
            <!-- 添加操作列 -->
            <el-table-column label="操作" width="80" fixed>
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  title="删除"
                ></el-button>
              </template>
            </el-table-column>
            <template v-for="(col, key, index) in cols">
              <el-table-column
                :type="col.type"
                :prop="col.prop"
                sortable
                :label="col.label"
                :width="col.width || 90"
                :key="index"
                :fixed="col.fixed"
              >
                <template slot-scope="scope">
                  <el-tag type="primary" v-if="col.type==='sort'">{{ scope.row.type }}</el-tag>
                  <!-- <i :class="{'el-icon-check': 'currentDataIsUpload' == col.prop && scope.row[col.prop]}"></i> -->
                  <i
                    v-if="'currentDataIsUpload' === col.prop"
                    :class="{'el-icon-check': scope.row[col.prop], 'el-icon-close': !scope.row[col.prop]}"
                  ></i>
                  <span v-if="'currentDataIsUpload' !== col.prop">{{ scope.row[col.prop] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 上传对话框 -->
    <el-dialog
      title="文件上传"
      :visible.sync="uploadDialog.visible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="uploadDialog.form" label-width="120px">
        <el-form-item label="选择文件">
          <el-upload
            ref="upload"
            :action="uploadAction"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :http-request="customUpload"
          >
            <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传一个文件</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="填报年份">
          <el-select v-model="uploadDialog.form.year" placeholder="请选择年份" style="width: 100%">
            <el-option
              v-for="item in uploadDialog.yearOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="填报季度">
          <el-select v-model="uploadDialog.form.quarter" placeholder="请选择季度" style="width: 100%">
            <el-option
              v-for="item in uploadDialog.quarterOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialog.visible = false" :disabled="uploadDialog.uploading">取消</el-button>
        <el-button type="primary" @click="handleUpload" :loading="uploadDialog.uploading">
          {{ uploadDialog.uploading ? '上传中...' : '确定上传' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style>
div.item-view-grid .el-icon-check {
  color: green;
}

div.item-view-grid .el-icon-close {
  color: red;
}
</style>

<script>
import globalVariables from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";
import StringUtil from "@/utils/StringUtil";

import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_qcIndexSourceView",

  props: ["fatherData"],
    data() {
    // 先定义cols数组
    const cols = [
      {
        label: "移植日期",
        prop: "lastSurgicalTime",
        type: "normal",
        width: 100,
        fixed: true
      },
      {
        label: "姓名",
        prop: "patientName",
        type: "normal",
        width: 60,
        fixed: true
      },
      {
        label: "住院号",
        prop: "mrn",
        type: "normal",
        width: 80,
        fixed: true
      },
      {
        label: "性别",
        prop: "gender",
        type: "normal",
        width: 60,
        fixed: true
      },
      {
        label: "民族",
        prop: "ethnicGroup",
        type: "normal",
        width: 60,
        fixed: true
      },
      {
        label: "出生日期",
        prop: "birthTime",
        type: "normal",
        width: 100,
        fixed: true
      },
      {
        label: "医院名称",
        prop: "org",
        type: "normal",
        width: 220,
        fixed: true
      },
      {
        label: "采集范围",
        prop: "orgPartName",
        type: "normal",
        width: 100,
        fixed: true
      },
      /*{ label: "医院定级", prop: "orgGrade", type: "normal", width: 120 },
      { label: "医院定等", prop: "orgClass", type: "normal", width: 120 },
      { label: "是否教学", prop: "isEducation", type: "normal", width: 120 },
      {
        label: "医院性质",
        prop: "hospitalNature",
        type: "normal",
        width: 120
      },
      { label: "医院单位码", prop: "orgCode", type: "normal", width: 160 },
      {
        label: "医院类别",
        prop: "medinstCategory",
        type: "normal",
        width: 120
      },
      {
        label: "编制床位数",
        prop: "fixedBedNum",
        type: "normal",
        width: 130
      },
      {
        label: "注册护士数",
        prop: "regNursesNum",
        type: "normal",
        width: 130
      },
      {
        label: "护理部电话",
        prop: "nursingDepartPhone",
        type: "normal",
        width: 130
      },
      {
        label: "负责人姓名",
        prop: "managerName",
        type: "normal",
        width: 160
      },
      {
        label: "负责人电话",
        prop: "managerPhone",
        type: "normal",
        width: 160
      },
      { label: "邮箱", prop: "managerEmail", type: "normal", width: 200 },
      {
        label: "单位审核通过时间",
        prop: "orgApprovalTime",
        type: "normal",
        width: 180
      },
      { label: "是否冻结", prop: "orgIsFrozen", type: "normal", width: 120 },
      {
        label: "最近一次冻结时间",
        prop: "orgLastFrozenTime",
        type: "normal",
        width: 180
      },
      {
        label: "最近一次解冻时间",
        prop: "orgLastUnfrozenTime",
        type: "normal",
        width: 180
      },
      {
        label: "开始填报季度",
        prop: "firstDataQuarter",
        type: "normal",
        width: 160
      },
      {
        label: "当前需填报季度",
        prop: "currentDataIsUpload",
        type: "normal",
        width: 180
      },
      {
        label: "是否进入样板库",
        prop: "isTemplateData",
        type: "normal",
        width: 140
      },*/
      {
        label: "填报季度",
        prop: "currentDataQuarter",
        type: "normal",
        width: 140,
        fixed: true
      },
      /*{ label: "提交状态",
        prop: "submitState",
        type: "normal",
        width: 140 },*/
      /*{
        label: "审核状态",
        prop: "checkState",
        type: "normal",
        width: 140,
        fixed: true
      }*/
    ];

    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      dataTableHeight: "100%",
      loading: false,
      formDisabled: true,
      showPaneHead: true,
      partUserFormDisabled: false,
      tableDataAction:
        globalVariables.cmis.domain + "/carePlatform/reportResult/list",
      //tableDataAction: "http://127.0.0.1:8880/rh-cmis/carePlatform/reportResult/list",
      tabs: {
        whole: "primary", //全院模版 1
        Ward: "", //病区模板  2
        ICU: "" //ICU模版  3
      },

      pickerOptions: {
        shortcuts: [
          {
            text: "本月",
            onClick(picker) {
              picker.$emit("pick", [
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                ),
                DateUtil.strToDate(
                  DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
                )
              ]);
            }
          },
          {
            text: "今年至今",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-01-01  00:00:00")
              );
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近六个月",
            onClick(picker) {
              const end = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              const start = DateUtil.strToDate(
                DateUtil.dateToStr(new Date(), "yyyy-MM-01  00:00:00")
              );
              start.setMonth(start.getMonth() - 6);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },


      form: {
        visible: PageUtil.searchFormVisibility(),
        combo_submitState: formConfig.combo_submitState,
        combo_orgId: formConfig.combo_orgIdHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        //combo_orgPartId: formConfig.combo_orgPartId,
        // combo_orgPartId: (function() {
        //   var tmp = formConfig.combo_orgPartId;
        //   if (tmp[0].label == "所有") tmp.shift();
        //   return tmp;
        // })(),
        combo_orgGrade: formConfig.combo_orgGrade,
        combo_yesno: formConfig.combo_yesno,
        combo_area: formConfig.combo_areaHierarchical,
        combo_checkState: formConfig.combo_checkState,
        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),
        combo_jobType: formConfig.combo_jobType,
        combo_gender: formConfig.combo_gender,
        combo_ethnicGroup: formConfig.combo_ethnicGroup,
        // combo_jobType: [
        //   { value: "1", label: "敏感指标任务" },
        //   { value: "4", label: "不良事件任务" },
        //   { value: "5", label: "优质护理任务" }
        //   //,{ value: "3", label: "档案任务" }
        // ],
        value_searchDataTime: [],
        value_surgicalFromDate: "",
        value_surgicalToDate: "",
        value_birthFromDate: "",
        value_birthToDate: "",
        value_patientName: "",
        value_mrn: "",
        value_gender: "",
        value_ethnicGroup: "",
        value_fromTime: "",
        value_toTime: "",
        value_submitState: "",
        value_enteredAt: formConfig.value_user.org_id,
        value_orgId: [formConfig.value_user.org_id],
        value_orgPartId: [""],
        value_orgName: "",
        value_orgGrade: "",
        value_checkState: "",
        value_nationalUpload: "",
        value_area: formConfig.value_user.org_area,
        value_reportTemplate: formConfig.value_user.hasOwnProperty("orgPart_id")
          ? formConfig.value_user.orgPart_template_id
          : 1,
        value_jobType: "1", //任务类型默认为敏感数据
        value_jobPeriod: "4", //任务周期默认为季度
        rules: {
          value_searchDataTime: [
            {required: true, message: "请选择开始截止时间"}
          ]
        }
      },
      cols: cols,
      colsLength: cols.length,

      // 上传相关数据
      uploadAction: globalVariables.fileUpload.domain + globalVariables.fileUpload.endpoint,
      uploadDialog: {
        visible: false,
        uploading: false,
        selectedFile: null,
        form: {
          year: '',
          quarter: ''
        },
        yearOptions: [],
        quarterOptions: []
      }
    };
  },

  watch: {
    // 监听年份变化，更新季度选项
    'uploadDialog.form.year': function(newYear) {
      this.updateQuarterOptions();
    }
  },

  mounted: function () {
    var self = this;

    self.layoutPage();
    //如果作为别人的子组件则隐藏头部标题
    if (null != self.fatherData) {
      self.showPaneHead = self.fatherData;
    }
    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    }

    var now = new Date();
    //初始开始, 结束时间
    //self.form.value_fromTime = now.getFullYear() + "-01-01";
    var from = DateUtil.dateAdd("q", -4, now);
    self.form.value_fromTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(from),
      "yyyy-MM-dd"
    );
    self.form.value_toTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(now),
      "yyyy-MM-dd"
    );

    var tempFromTime = new Date();
    tempFromTime = DateUtil.strToDate(
      DateUtil.dateToStr(tempFromTime, "yyyy-MM-01  00:00:00")
    );
    var tempTimeSpace = [DateUtil.dateAdd("m", -3, tempFromTime), tempFromTime];

    self.form.value_searchDataTime = tempTimeSpace;

    //初始化移植日期范围（默认为空）
    self.form.value_surgicalFromDate = "";
    self.form.value_surgicalToDate = "";

    //初始化出生日期范围（默认为空）
    self.form.value_birthFromDate = "";
    self.form.value_birthToDate = "";

    //初始化 单位、病区
    self.form["value_orgId"] = StringUtil.getIdsById(
      formConfig.value_orgId,
      formConfig.value_user.org_id
    );

    // self.form["value_orgPartId"] = StringUtil.getIdsById(
    //   formConfig.value_orgPartId,
    //   formConfig.value_user.orgPart_id
    // );
    // //如果是医院管理员manager 就可以编辑病区
    // if (1 == formConfig.value_user.orgPart_id) {
    //   self.partUserFormDisabled = false;
    // }

    //参数
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);
    //
    var k, v;
    for (k in formValues) {
      v = formValues[k];
      if (!v) {
        continue;
      }
      if ("area" == k) {
        v = StringUtil.getIdsById(formConfig.value_areaId, v);
      } else if ("orgPartId" == k) {
        self.form.value_reportTemplate =
          formConfig.value_orgPartId[v].templateId;

        v = StringUtil.getIdsById(formConfig.value_orgPartId, v);
      } else if ("orgId" == k) {
        v = StringUtil.getIdsById(formConfig.value_orgId, v);
      }
      self.form["value_" + k] = v;
    }
    self.form.value_jobType += "";
    self.form.value_jobPeriod =
      formConfig.value_jobType[self.form.value_jobType].period;
    //省级管理员不指定机构
    if (1 == formConfig.value_user.org_id) {
      self.form["value_orgId"] = [""];
    }
    //接受参数传入数据的开始时间与结束时间
    if (null != formValues["fromTime"]) {
      tempTimeSpace[0] = new Date(formValues["fromTime"]);
      self.form.value_fromTime = DateUtil.dateToStr(
        DateUtil.quarterHeadDate(new Date(formValues["fromTime"])),
        "yyyy-MM-dd"
      );
    }
    if (null != formValues["toTime"]) {
      tempTimeSpace[1] = new Date(formValues["toTime"]);
      self.form.value_toTime = DateUtil.dateToStr(
        DateUtil.quarterHeadDate(new Date(formValues["toTime"])),
        "yyyy-MM-dd"
      );
    }
    self.form.value_searchDataTime = tempTimeSpace;

    //处理移植日期参数
    if (null != formValues["surgicalFromTime"]) {
      self.form.value_surgicalFromDate = formValues["surgicalFromTime"];
    }
    if (null != formValues["surgicalToTime"]) {
      self.form.value_surgicalToDate = formValues["surgicalToTime"];
    }

    //处理患者姓名参数
    if (null != formValues["patientName"]) {
      self.form.value_patientName = formValues["patientName"];
    }

    //处理住院号参数
    if (null != formValues["mrn"]) {
      self.form.value_mrn = formValues["mrn"];
    }

    //处理性别参数
    if (null != formValues["gender"]) {
      self.form.value_gender = formValues["gender"];
    }

    //处理民族参数
    if (null != formValues["ethnicGroup"]) {
      self.form.value_ethnicGroup = formValues["ethnicGroup"];
    }

    //处理出生日期参数
    if (null != formValues["birthFromTime"]) {
      self.form.value_birthFromDate = formValues["birthFromTime"];
    }
    if (null != formValues["birthToTime"]) {
      self.form.value_birthToDate = formValues["birthToTime"];
    }

    //通过科室找到上报模板
    if (null != self.form.value_orgPartId) {
      switch (self.form.value_reportTemplate) {
        case 1:
          self.pushWhole();
          break;
        case 2:
          self.pushWard();
          break;
        case 3:
          self.pushICU();
          break;
      }
    } else this.getTableData();

    // 初始化上传选项
    self.initUploadOptions();
  },

  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    pushWhole() {
      var self = this;

      self.tabs.whole = "primary";
      self.tabs.ICU = "";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 1;
      self.getTableData();
    },

    pushWard() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "";
      self.tabs.ward = "primary";
      self.form.value_reportTemplate = 2;
      self.getTableData();
    },

    pushICU() {
      var self = this;

      self.tabs.whole = "";
      self.tabs.ICU = "primary";
      self.tabs.ward = "";
      self.form.value_reportTemplate = 3;
      self.getTableData();
    },
    getTableData: function () {
      var self = this;

      self.loading = true;
      self.tableData = [];
      var lastNode = StringUtil.getLastNode(self.form.value_area);

      //表单验证 按jobType赋值数据开始截止时间
      var fromTime, toTime;
      if ("4" == self.form.value_jobPeriod) {
        fromTime = self.form.value_fromTime || null;
        toTime = self.form.value_toTime || null;
      } else {
        //数据时间 转换赋值
        fromTime = typeof self.form.value_searchDataTime[0] === 'string' ?
          self.form.value_searchDataTime[0] :
          DateUtil.dateToStr(self.form.value_searchDataTime[0]);
        toTime = typeof self.form.value_searchDataTime[1] === 'string' ?
          self.form.value_searchDataTime[1] :
          DateUtil.dateToStr(self.form.value_searchDataTime[1]);
      }
      //处理移植日期筛选参数
      var surgicalFromTime = null, surgicalToTime = null;
      if (self.form.value_surgicalFromDate && self.form.value_surgicalFromDate !== "") {
        surgicalFromTime = self.form.value_surgicalFromDate;
      }
      if (self.form.value_surgicalToDate && self.form.value_surgicalToDate !== "") {
        surgicalToTime = self.form.value_surgicalToDate;
      }

      //处理出生日期筛选参数
      var birthFromTime = null, birthToTime = null;
      if (self.form.value_birthFromDate && self.form.value_birthFromDate !== "") {
        birthFromTime = self.form.value_birthFromDate;
      }
      if (self.form.value_birthToDate && self.form.value_birthToDate !== "") {
        birthToTime = self.form.value_birthToDate;
      }

      //请求数据
      var requestData = {
        pageNo: self.currentPage,
        pageSize: self.pageSize,

        //enteredAt: self.form.value_enteredAt,  self.form.value_checkState

        orgId: StringUtil.getLastNode(self.form.value_orgId),
        orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
        org: self.form.value_orgName,
        checkState: self.form.value_checkState,
        orgGrade: self.form.value_orgGrade,
        submitState: self.form.value_submitState,
        nationalUpload: self.form.value_nationalUpload,
        "orgPartObj.extend.extendI4": self.form.value_reportTemplate,
        area: StringUtil.getTreeNames(formConfig.value_areaId, lastNode),
        jobType: self.form.value_jobType,
        // 患者姓名筛选参数
        patientName: self.form.value_patientName,
        // 住院号筛选参数
        mrn: self.form.value_mrn,
        // 性别筛选参数
        gender: self.form.value_gender,
        // 民族筛选参数
        ethnicGroup: self.form.value_ethnicGroup
      };

      // 只有当时间参数不为null时才添加到请求数据中
      if (fromTime !== null && fromTime !== "") {
        requestData.dataFromTime = fromTime;
      }
      if (toTime !== null && toTime !== "") {
        requestData.dataToTime = toTime;
      }

      // 移植日期筛选参数
      if (surgicalFromTime !== null && surgicalFromTime !== "") {
        requestData.surgicalFromTime = surgicalFromTime;
      }
      if (surgicalToTime !== null && surgicalToTime !== "") {
        requestData.surgicalToTime = surgicalToTime;
      }

      // 出生日期筛选参数
      if (birthFromTime !== null && birthFromTime !== "") {
        requestData.birthFromTime = birthFromTime;
      }
      if (birthToTime !== null && birthToTime !== "") {
        requestData.birthToTime = birthToTime;
      }

      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        type: "post",
        url: self.tableDataAction,
        data: requestData,
        dataType: "json",
        success: function (res) {
          self.loading = false;

          //self.tableData = res.list;
          self.total = res.count;
          //读取指标
          self.getIndexItems(res.list);
        },
        error: function () {
          self.loading = false;

          self.$alert("加载失败");
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 读取指标
     */
    getIndexItems: function (orgData) {
      var self = this;
      //是否有单位档案
      if (0 >= self.total) {
        return;
      }
      //保留单位档案列
      var colDocNum = self.cols.length;
      var cols = self.cols;
      //self.cols = cols;
      //表单验证
      //表单验证 按jobType赋值数据开始截止时间
      var fromTime, toTime;
      if ("4" === self.form.value_jobPeriod) {
        fromTime = self.form.value_fromTime || null;
        toTime = self.form.value_toTime || null;
      } else {
        //表单验证
        if (!self.form.value_searchDataTime) {
          self.$alert('请选择"开始时间"和"截止时间".');
          return;
        }
        //数据时间 转换赋值
        fromTime = typeof self.form.value_searchDataTime[0] === 'string' ?
          self.form.value_searchDataTime[0] :
          DateUtil.dateToStr(self.form.value_searchDataTime[0]);
        toTime = typeof self.form.value_searchDataTime[1] === 'string' ?
          self.form.value_searchDataTime[1] :
          DateUtil.dateToStr(self.form.value_searchDataTime[1]);
      }
      //console.log(self.form);

      var indexRequestData = {
        reportResultIds: orgData.map(item => item.reportResultId),
        enteredAt: self.form.value_enteredAt,
        orgId: StringUtil.getLastNode(self.form.value_orgId),
        orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
        "orgPart.extend.extendI4": self.form.value_reportTemplate,
        itemTypeName: "原始数据",
        jobType: self.form.value_jobType
      };

      // 只有当时间参数不为null时才添加到请求数据中
      if (fromTime !== null && fromTime !== "") {
        indexRequestData["quarterRange.fromTime"] = fromTime;
      }
      if (toTime !== null && toTime !== "") {
        indexRequestData["quarterRange.toTime"] = toTime;
      }

      var ajaxOptsIdx = {
        url: globalVariables.cmis.domain + "/carePlatform/index/getItemsList",
        //url: 'http://127.0.0.1:8880/rh-cmis/carePlatform/index/getItemsList'
        type: "post",
        dataType: "json",
        traditional: true,
        data: indexRequestData,
        complete: function () {
          self.loading = false;
        },
        success: function (res) {
          console.log('获取原始数据', res);
          var idxItems = res ? res["data"] : null;
          if (!idxItems || !idxItems.length) {
            self.tableData = orgData;
            return;
          }
          var tableData = orgData; //self.tableData;
          var re = /\./;
          for (var j = 0, itm, prop, iprop; j < idxItems.length; j++) {
            itm = idxItems[j];

            if (!re.test(itm["code"])) {
              //过滤掉code不含'.'的二级标题
              //console.log(itm);
              continue;
            }
            //
            prop = "index" + itm["code"]; //itm["id"];
            iprop = false; //是否有了这个指标列
            for (var j1 = self.colsLength; j1 < cols.length; j1++) {
              if ((iprop = prop === cols[j1]["prop"])) {
                break;
              }
            }
            if (!iprop) {
              cols.push({
                label: itm["name"],
                prop: prop,
                type: "normal",
                width: 180
              });
            }
            if (!itm["fromTime"]) {
              continue;
            }
            //
            var dt = DateUtil.quarterLabelOfDate(
              DateUtil.strToDate(itm["fromTime"])
            );
            //
            for (var k = 0, tr; k < tableData.length; k++) {
              tr = tableData[k];
              //对比单位, 可是病区, 填报季度
              if (
                tr["orgId"] === itm["organizationId"] &&
                tr["orgPartId"] === itm["orgPartId"]
              ) {
                if (tr.reportResultId === itm.reportResultId) {
                  tr["currentDataIsUpload"] = 1;
                  tr[prop] = itm["value"];
                }
              }
            }
          }
          //更新表头
          self.cols = cols;
          //更新表格数据
          self.tableData = tableData;
          //更新表头引起表格布局错位, 重新布局
          setTimeout(function () {
            self.$refs.dataTable && self.$refs.dataTable.doLayout();
          }, 0);
        }
      };
      //
      self.loading = true;
      //
      AjaxUtil.send(ajaxOptsIdx);
    },

    onSearch: function () {
      this.getTableData();
    },

    swapFormVisibility: function () {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function () {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    },

    selectJobType: function (params) {
      self = this;
      // params = params.split(",");
      // let value = params[0]; // 获取到的value
      // let label = params[1]; // 获取到的label
      //var lastjobPeriod = self.form.value_jobPeriod;
      self.form.value_jobPeriod =
        formConfig.value_jobType[self.form.value_jobType].period;
      // if ("4" == self.form.value_jobPeriod && "4" != lastjobPeriod) {
      // }
    },

    handleDelete: function (row) {
      var self = this;
      self.$confirm("确定要删除该条数据吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        var requestData = {
          id: row.reportResultId
        };
        var ajaxOpts = {
          acrossDomian: true,
          xhrFields: {
            withCredentials: true
          },
          type: "post",
          url: globalVariables.cmis.domain + "/carePlatform/reportResult/delete",
          data: requestData,
          dataType: "json",
          success: function (res) {
            if (res.result === "true") {
              self.$message({
                type: "success",
                message: "删除成功!"
              });
              self.getTableData();
            } else {
              self.$message({
                type: "error",
                message: res.message || "删除失败"
              });
            }
          },
          error: function () {
            self.$message({
              type: "error",
              message: "删除失败"
            });
          }
        };
        AjaxUtil.send(ajaxOpts);
      });
    },

    // 初始化上传选项
    initUploadOptions: function() {
      var self = this;
      var currentYear = new Date().getFullYear();

      // 初始化年份选项（今年和去年）
      self.uploadDialog.yearOptions = [
        { value: currentYear, label: currentYear + '年' },
        { value: currentYear - 1, label: (currentYear - 1) + '年' }
      ];

      // 设置默认年份为今年
      self.uploadDialog.form.year = currentYear;

      // 初始化季度选项
      self.updateQuarterOptions();
    },

    // 更新季度选项（根据当前日期限制）
    updateQuarterOptions: function() {
      var self = this;
      var currentDate = new Date();
      var currentYear = currentDate.getFullYear();
      var currentQuarter = Math.floor(currentDate.getMonth() / 3) + 1;

      var quarterOptions = [];
      var selectedYear = self.uploadDialog.form.year;

      // 如果选择的是今年，则季度不能超过当前季度
      var maxQuarter = (selectedYear === currentYear) ? currentQuarter : 4;

      for (var i = 1; i <= maxQuarter; i++) {
        quarterOptions.push({
          value: i,
          label: '第' + i + '季度'
        });
      }

      self.uploadDialog.quarterOptions = quarterOptions;

      // 如果当前选择的季度超过了最大季度，重置为第一季度
      if (self.uploadDialog.form.quarter > maxQuarter) {
        self.uploadDialog.form.quarter = '';
      }
    },

    // 显示上传对话框
    showUploadDialog: function() {
      var self = this;
      self.uploadDialog.visible = true;
      self.uploadDialog.form.year = new Date().getFullYear();
      self.uploadDialog.form.quarter = '';
      self.uploadDialog.selectedFile = null;
      // 清空文件选择
      self.clearUploadFile();
      self.updateQuarterOptions();
    },

    // 文件选择变化
    handleFileChange: function(file, fileList) {
      this.uploadDialog.selectedFile = file.raw;
    },

    // 文件移除
    handleFileRemove: function(file, fileList) {
      this.uploadDialog.selectedFile = null;
    },

    // 上传前验证
    beforeUpload: function(file) {
      // 这里可以添加文件类型和大小验证
      return true;
    },

    // 自定义上传
    customUpload: function(option) {
      var self = this;

      if (!self.uploadDialog.selectedFile) {
        self.$message.error('请选择要上传的文件');
        return;
      }

      if (!self.uploadDialog.form.year) {
        self.$message.error('请选择报告年份');
        return;
      }

      if (!self.uploadDialog.form.quarter) {
        self.$message.error('请选择报告季度');
        return;
      }

      // 文件大小检查（可选）
      var maxSize = 50 * 1024 * 1024; // 50MB
      if (self.uploadDialog.selectedFile.size > maxSize) {
        self.$message.error('文件大小不能超过50MB');
        return;
      }

      // 计算季度的开始和结束时间
      var year = self.uploadDialog.form.year;
      var quarter = self.uploadDialog.form.quarter;

      var fromTime = self.getQuarterStartTime(year, quarter);
      var toTime = self.getQuarterEndTime(year, quarter);

      // 创建FormData
      var formData = new FormData();
      formData.append('file', self.uploadDialog.selectedFile);
      formData.append('fromTime', fromTime);
      formData.append('toTime', toTime);

      self.uploadDialog.uploading = true;

      // 使用jQuery Ajax上传，自动携带认证信息
      var ajaxOpts = {
        acrossDomian: true,
        type: "post",
        url: self.uploadAction,
        data: formData,
        processData: false, // 不处理数据
        contentType: false, // 不设置内容类型
        timeout: 30000, // 30秒超时
        success: function(response) {
          self.uploadDialog.uploading = false;

          try {
            // 如果response是字符串，需要先解析成对象
            var result = typeof response === 'string' ? JSON.parse(response) : response;

            // 根据返回的JSON数据判断上传结果
            if (result.result === "true") {
              var fileName = self.uploadDialog.selectedFile.name;
              var year = self.uploadDialog.form.year;
              var quarter = self.uploadDialog.form.quarter;
              var successMsg = result.message ||
                '文件"' + fileName + '"上传成功！(' + year + '年第' + quarter + '季度)';

              self.$message.success(successMsg);
              self.uploadDialog.visible = false;
              // 清空文件选择
              self.clearUploadFile();
              // 刷新表格数据
              self.getTableData();
            } else {
              // 上传失败，显示服务器返回的错误信息
              self.$message.error(result.message || '上传失败');
            }
          } catch (e) {
            // JSON解析失败
            self.$message.error('服务器响应格式错误：' + e.message);
          }
        },
        error: function(xhr, status, error) {
          self.uploadDialog.uploading = false;

          if (status === 'timeout') {
            self.$message.error('上传超时，请重试');
          } else if (xhr.status === 0) {
            self.$message.error('网络错误，请检查网络连接或服务器是否可访问');
          } else {
            self.$message.error('上传失败：HTTP ' + xhr.status + ' - ' + error);
          }
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    // 处理上传
    handleUpload: function() {
      this.customUpload();
    },

    // 获取季度开始时间
    getQuarterStartTime: function(year, quarter) {
      var month = (quarter - 1) * 3 + 1;
      return year + '-' + (month < 10 ? '0' + month : month) + '-01 00:00:00';
    },

    // 获取季度结束时间
    getQuarterEndTime: function(year, quarter) {
      var nextQuarter = quarter + 1;
      var nextYear = year;
      if (nextQuarter > 4) {
        nextQuarter = 1;
        nextYear = year + 1;
      }
      var month = (nextQuarter - 1) * 3 + 1;
      return nextYear + '-' + (month < 10 ? '0' + month : month) + '-01 00:00:00';
    },

    // 清空文件选择
    clearUploadFile: function() {
      var self = this;
      self.uploadDialog.selectedFile = null;
      // 清空el-upload组件的文件列表
      if (self.$refs.upload) {
        self.$refs.upload.clearFiles();
      }
    }
  }
};
</script>

