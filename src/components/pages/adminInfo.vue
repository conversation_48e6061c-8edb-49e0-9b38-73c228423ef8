<template>
  <div>
  <el-container>
    <el-main class="data-body">
	  <div>
        <el-row class="pane-head">
          <el-col :span="21" class="pane-head-label"><i class="el-icon-menu"></i>&nbsp;省级质控管理员信息</el-col>
          <el-col :span="3">
            
          </el-col>
        </el-row>
	  </div>
	  <div class="content-pane" style="margin:0 auto;width:80%;min-width:680px;">
        <el-form ref="mainForm" :model="form" label-width="120px">
					
          <el-row class="form-row">
					<el-col :span="21">
						&nbsp;
					</el-col>
					<el-col :span="3">
						<el-form-item>
						</el-form-item>
					</el-col>
			
          </el-row>

          <el-row class="form-row">
					<el-col :span="10">
						<div class="lft-col">
							<el-form-item>
								<img :src="form.avatar" :alt="form.userName" />
							</el-form-item>
							<el-form-item>
								<span>{{form.userName}}</span>
							</el-form-item>
							<el-form-item>
								<el-radio-group v-model="form.sex">
									<el-radio label="1">男</el-radio>
									<el-radio label="2">女</el-radio>
								</el-radio-group>
							</el-form-item>
							<el-form-item>
								<!-- <el-button type="primary" size="mini" disabled title="暂不支持">修改头像</el-button> -->
							</el-form-item>
						</div>
					</el-col>

					<el-col :span="14">
						<el-form-item label="用户姓名">
							<el-input type="text" v-model="form.userName" :disabled="true"></el-input>
						</el-form-item>
						
						<el-form-item label="电子邮箱">
							<el-input type="text" v-model="form.email" :disabled="true"></el-input>
						</el-form-item>
						
						<el-form-item label="手机号码">
							<el-input type="text" v-model="form.mobile" :disabled="true"></el-input>
						</el-form-item>
						
						<el-form-item label="办公室电话">
							<el-input type="text" v-model="form.phone" :disabled="true"></el-input>
						</el-form-item>
						
						<!-- <el-form-item label="个性签名">
							<el-input type="textarea" v-model="form.sign" :disabled="true"></el-input>
						</el-form-item> -->
						
					</el-col>
			
          </el-row>

        </el-form>        
	  </div>
	
	</el-main>
  </el-container>  
  </div>
</template>

<style type="text/css">
.lft-col{
	margin:0 auto;
	width:480px;
	text-align:center;
}
div.lft-col .el-form-item__content{
	margin-left:0 !important;
}
.box-noborder{
	border-top-width: 0;
	border-width: 0;
}
</style>

<script>

  import global from '@/config/global-config'
	
  import AjaxUtil from '@/utils/AjaxUtil'
	import LoginHelper from "@/utils/LoginHelper";

  export default {
    name: "page_adminInfo",
  
    data () {
      return {

				loading: false, 
		
				form: {
					userName: ""
					, email: ""
					, mobile: ""
					, phone: ""
					, sign: ""
					, sex: "2"
					, avatar: global.cmis.domain + global.userAvatarWomen,
				},
				avatars:{
                    1:global.cmis.domain + global.userAvatar,
                    2:global.cmis.domain + global.userAvatarWomen
                },
				tableData:[],
				tableDataAction:global.cmis.domain + '/carePlatform/index/readUserInfo',
      }
    },
    created: function(){
			this.readData();
		},
    methods: {
	readData: function() {
      var self = this;
      self.loading = true;
      //请求数据
      AjaxUtil.send({
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        type: "post",
        url: self.tableDataAction,
        data: {
          
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
		  self.tableData = res;
		  if(self.tableData.length==0)
			  return;

		  self.form.userName=self.tableData[0].dictLabel;
		  self.form.mobile=self.tableData[0].mobile;
		  self.form.phone=self.tableData[0].tel;
		  self.form.email=self.tableData[0].mail;
		  self.form.sex = self.tableData[0].sex;
		  if(self.form.sex=='1' || self.form.sex=='2')
		  {
			  self.form.avatar= self.avatars[self.form.sex];
		  }
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      });
    },
		}
  }
</script>