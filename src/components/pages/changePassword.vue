<template>
  <div>
  <el-container>
    <el-main class="data-body">
	  <div>
        <el-row class="pane-head">
          <el-col :span="21" class="pane-head-label"><i class="el-icon-menu"></i>&nbsp;修改密码</el-col>
          <el-col :span="3">
            
          </el-col>
        </el-row>
	  </div>
	  <div class="content-pane" style="margin:0 auto;width:640px">
        <el-form ref="mainForm" :model="form" :rules="formRules" label-width="120px">
					
          <el-row class="form-row">
					<el-col :span="21">
						&nbsp;
					</el-col>
					<el-col :span="3">

						<el-form-item>

              <el-button type="primary" @click="prepareChangePassword('mainForm')" size="mini" :loading="loading">保存</el-button>
            </el-form-item>
					</el-col>
			
          </el-row>
          <el-row class="form-row">
					<el-col :span="21">
						<el-form-item label="旧密码" prop='oldPassword'>
							<el-input type="password" v-model="form.oldPassword"></el-input>
						</el-form-item>
						
						<el-form-item label="新密码" prop='newPassword'>
							<el-input type="password" v-model="form.newPassword"></el-input>
						</el-form-item>
						
						<el-form-item label="确认新密码" prop='confirmNewPassword'>
							<el-input type="password" v-model="form.confirmNewPassword"></el-input>
						</el-form-item>
						
					</el-col>
			
          </el-row>
        </el-form>        
	  </div>
	
	</el-main>
  </el-container>  

  </div>
</template>

<script>

  import global from '@/config/global-config'
	
  import AjaxUtil from '@/utils/AjaxUtil'
  import LoginHelper from "@/utils/LoginHelper";
  import {validatePasswd} from "@/utils/ValidatorUtil";

  export default {
    name: "page_changePassword",

    data () {
	  var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.form.newPassword) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
				loading: false
				, form: {
					oldPassword: ""
					, newPassword: ""
					, confirmNewPassword: ""
				}
				, formRules: {
					oldPassword: [{ required: true, message: '请输入旧密码' }],
					newPassword: [{ required: true, message: '请输入新密码' },
					{ min: 8, max: 16, message: '密码长度在8到16个字符', trigger: 'blur' },
					{validator:validatePasswd,trigger:'blur'}],
					confirmNewPassword: [{ required: true, message: '请输入确认新密码' },
					{validator:validatePass2,trigger:'blur'}]
				}
      }
    },
    created: function(){

    },
    methods: {
			prepareChangePassword: function(formName) {
				var self = this;
                this.$refs[formName].validate((valid) => {
          if (valid) {
            self.$confirm("是否确定修改密码?")
          .then(self.changePassword);
          } else {
			alert("密码校验失败，请重新设置密码后再提交！");
            console.log('error submit!!');
            return false;
          }
        })

			}
	 		, changePassword: function() {
				var self = this;
				
				var ajaxOpts = {
					url: global.cmis.domain + "/sso/auth/changePassword"
					, type: "post"
					, data: {
						oldPassword: self.form.oldPassword
						, newPassword: self.form.newPassword
						, confirmNewPassword: self.form.confirmNewPassword
					}
					, dataType: "json"
					, success: function(res) {
						self.loading = false;
						if(!res || "true" != res["result"]) {
							if(res) {
								self.$alert(res["message"]);
							}
							return;
						}

						self.$alert("修改成功, 请重新登录.", {
							callback: function() {
								LoginHelper.logout(self.$router);
							}
						});
					}
					, error: function() {
						self.loading = false;						
					}
				};
				
				AjaxUtil.send(ajaxOpts);
			}
		}
  }
</script>