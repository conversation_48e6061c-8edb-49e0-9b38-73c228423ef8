<template>
  <div id="page_averageMedianStat">
    <el-container>
      <el-aside width="200px">
        <el-menu :default-active="form.value_indexId+''" class="el-menu-vertical" unique-opened @select="handleSelect" >
          <template v-for="(menu, index) in form.menu_index[form.value_reportTemplate]">
            <el-submenu :index="''+index" :key="index">
              <template slot="title">
                <i class="el-icon-menu"></i>
                <span slot="title">{{menu.name}}</span>
              </template>
              <template v-for="(val,keyItem,idx) in menu.idxVal">
                <el-menu-item :index="val.dictCode" :key="idx">
                  <i class="el-icon-s-data"></i>
                  {{val.dictLabel}}
                </el-menu-item>
              </template>
            </el-submenu>
          </template>
        </el-menu>
      </el-aside>
      <el-container>
        <el-header style="padding:0;height:auto;">
          <div>
            <el-row class="pane-head">
              <el-col :span="6" class="pane-head-label" id="headName">
                <i class="el-icon-menu"></i>
                {{form.value_idxName}}
              </el-col>
              <el-col :span="3">
                <el-button
                  size="mini"
                  class="tab-button"
                  :type="tabs.selfCityType"
                  :disabled="tabs.selfCityDisabled"
                  @click="getSelfCity"
                >本市</el-button>
                <el-button
                  size="mini"
                  class="tab-button"
                  :type="tabs.selfProvType"
                  :disabled="tabs.selfProvDisabled"
                  @click="getSelfProvince"
                >本省</el-button>
              </el-col>
              <el-col :span="14">
                <el-button size="mini">导出</el-button>
                <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
              </el-col>
            </el-row>
          </div>
          <div class="search-form-pane" :style="{display: form.visible? '' : 'none'}">
            <el-form :inline="true" class="search-form-inline">
              <el-row class="form-row">
                <el-col :span="21">
                  <!-- <el-form-item label="单位" class="form-h-item">
                  <el-select
                    v-model="form.value_orgId"
                    placeholder="全部"
                    size="mini"
                    :disabled="formDisabled"
                  >
                    <el-option
                      v-for="item in form.combo_orgId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  </el-form-item>-->
                  <el-form-item label="单位" class="form-h-item">
                    <el-cascader
                      v-model="form.value_orgId"
                      placeholder="试试搜索"
                      size="mini"
                      :options="form.combo_orgId"
                      filterable
                      change-on-select
                      :show-all-levels="false"
                      :disabled="formDisabled"
                    ></el-cascader>
                  </el-form-item>
                  <!-- <el-form-item label="科室病区" class="form-h-item">
                  <el-select v-model="form.value_orgPartLable" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_orgPartId"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  </el-form-item>-->
                  <el-form-item label="科室病区" class="form-h-item">
                    <el-cascader
                      v-model="form.value_orgPartId"
                      placeholder="试试搜索"
                      size="mini"
                      :options="form.combo_orgPartId"
                      filterable
                      change-on-select
                      :show-all-levels="false"
                      v-on:change="changeOrgpart"
                    ></el-cascader>
                  </el-form-item>

                  <el-form-item label="开始年份" class="form-h-item">
                    <el-select v-model="form.value_fromYear" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_fromYear"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="开始季度" class="form-h-item">
                    <el-select v-model="form.value_fromQuarter" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_fromQuarter"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="截止年份" class="form-h-item">
                    <el-select v-model="form.value_toYear" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_toYear"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="截止季度" class="form-h-item">
                    <el-select v-model="form.value_toQuarter" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_toQuarter"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="上报国家" class="form-h-item">
                    <el-select v-model="form.value_yesno" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_yesno"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="单位类型" class="form-h-item">
                    <el-select v-model="form.value_medinstCategory" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_medinstCategory"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="所有制" class="form-h-item" >
                    <el-select v-model="form.value_orgProp" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_orgProp"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="隶属类型" class="form-h-item">
                    <el-select v-model="form.value_orgAttachType" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_orgAttachType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="单位定级" class="form-h-item">
                    <el-select v-model="form.value_orgGrade" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_orgGrade"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="床位数" class="form-h-item">
                    <el-select v-model="form.value_BedNumberScope" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_BedNumberScope"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="临床教学基地类型" class="form-h-item" label-width="140px">
                    <el-select v-model="form.value_clinicaledutype" placeholder="全部" size="mini">
                      <el-option
                        v-for="item in form.combo_clinicaledutype"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item class="form-h-item">
                    <el-button type="primary" size="mini" @click="getAverMedianData">查询</el-button>
                    <el-button size="mini">重置</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-header>
        <el-main style="padding:0" class="data-body">
          <div class="content-pane">
            <el-table
              :data="tableAverMedData"
              class="tb-edit"
              style="width: 100%"
              highlight-current-row
              stripe
              border
              size="mini"
              @row-click="handleRowClick"
              v-loading="loading"
            >
              <el-table-column label="季度" prop="timeStr" width="160"/>
              <el-table-column label="本院指标值" prop="idxVal" width="160"/>
              <el-table-column label="符合条件科室数" prop="departCnt" width="160"/>
              <el-table-column label="最小值" prop="minVal" width="160"/>
              <el-table-column label="10%分位数" prop="quantile10" width="160"/>
              <el-table-column label="25%分位数" prop="quantile25" width="160"/>
              <el-table-column label="50%分位数" prop="quantile50" width="160"/>
              <el-table-column label="75%分位数" prop="quantile75" width="160"/>
              <el-table-column label="90%分位数" prop="quantile90" width="160"/>
              <el-table-column label="最大值" prop="maxVal" width="160"/>
            </el-table>
          </div>
          <div class="content pb0 viewWrapper">
            <!-- Chart boxes -->
            <div class="row">
              <div class="col-md-12">
                <div class="box box-widget">
                  <div class="box-header with-border">
                    <h3 class="box-title">{{form.value_idxName}}指标占比</h3>
                    <div class="box-tools pull-right">
                      <div class="btn-group">
                        <button
                          type="button"
                          class="btn btn-box-tool dropdown-toggle"
                          data-toggle="dropdown"
                        >
                          <i class="fa fa-bars"></i>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                          <li>
                            <a href="#">导出Excel</a>
                          </li>
                          <li>
                            <a href="#">导出Word</a>
                          </li>
                          <li class="divider"></li>
                          <li>
                            <a href="#">基本设置</a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="box-body">
                    <div class="row">
                      <div>
                        <div class="chart">
                          <div id="statQuantileState" style="height:300px;"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row" :style="{display: form.groupVisible? '' : 'none'}">
                  <div class="col-md-12">
                    <div class="box box-widget">
                      <div class="box-header with-border">
                        <h3 class="box-title">{{form.value_idxName}}表</h3>
                        <div class="box-tools pull-right">
                          <div class="btn-group">
                            <button
                              type="button"
                              class="btn btn-box-tool dropdown-toggle"
                              data-toggle="dropdown"
                            >
                              <i class="fa fa-bars"></i>
                            </button>
                            <ul class="dropdown-menu" role="menu">
                              <li>
                                <a href="#">导出Excel</a>
                              </li>
                              <li>
                                <a href="#">导出Word</a>
                              </li>
                              <li class="divider"></li>
                              <li>
                                <a href="#">基本设置</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div class="box-body">
                        <div class="row">
                          <div>
                            <div class="chart">
                              <div id="statPercent">
                                <el-table
                                  :data="tableGroupPercentData"
                                  class="tb-edit"
                                  style="width: 100%"
                                  highlight-current-row
                                  stripe
                                  border
                                  size="mini"
                                  @row-click="handleRowClick"
                                  v-loading="loading"
                                >
                                  <template v-for="(col, key, index) in cols">
                                    <el-table-column
                                      :prop="col.prop"
                                      :label="col.label"
                                      :width="160"
                                      :key="index"
                                    />
                                  </template>
                                </el-table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-12">
                    <div class="box box-widget">
                      <div class="box-header with-border">
                        <h3 class="box-title">{{form.value_idxName}}分布图</h3>
                        <div class="box-tools pull-right">
                          <div class="btn-group">
                            <button
                              type="button"
                              class="btn btn-box-tool dropdown-toggle"
                              data-toggle="dropdown"
                            >
                              <i class="fa fa-bars"></i>
                            </button>
                            <ul class="dropdown-menu" role="menu">
                              <li>
                                <a href="#">导出Excel</a>
                              </li>
                              <li>
                                <a href="#">导出Word</a>
                              </li>
                              <li class="divider"></li>
                              <li>
                                <a href="#">基本设置</a>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <div class="box-body">
                        <div class="row">
                          <div>
                            <div class="chart">
                              <div id="statPercentChart" style="height:300px;"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--
        <div class="paginationClass">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
          -->
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style type="text/css">
div#page_averageMedianStat aside.el-aside {
  background-color: #d3dce6;
  color: #333;
  text-align: center;
  line-height: 200px;
}

/* 图形 */
div.viewWrapper {
  padding: 2px;
}
.viewWrapper .row {
  margin-left: 0;
  margin-right: 0;
}
.viewWrapper .box-widget {
  margin-bottom: 2px;
}
.viewWrapper .box-header .box-title {
  display: block;
  margin: 0 0px;
  text-align: center;
}
.viewWrapper .box-body {
  padding: 1px;
}

.viewWrapper .col-md-12,
.viewWrapper .col-md-5,
.viewWrapper .col-md-7 {
  padding-right: 2px;
  padding-left: 2px;
}

.viewDataSheet .ui-widget-content {
  border-width: 0;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-hdiv {
  border-color: #949397;
}
.viewDataSheet .ui-jqgrid-htable thead tr,
.viewDataSheet .ui-jqgrid-hdiv,
.viewDataSheet .ui-jqgrid-hbox {
  background-color: #6b7984;
}
.viewDataSheet .ui-jqgrid .ui-jqgrid-labels th {
  border-color: #949397 !important;
}
.viewDataSheet .ui-jqgrid-sortable {
  color: #f9fefe;
}

.viewDataSheet .ui-jqgrid-bdiv .ui-priority-secondary {
  background-color: #d2e4e6;
}
.fa-red {
  color: red;
}
.fa-green {
  color: green;
}

.el-menu-vertical:not(.el-menu--collapse) {
    width: 200px;
    max-height: 600px;
    text-align:left;
  }
</style>

<script>
import global from "@/config/global-config";
import orgTree from "@/config/orgTree-config";
import DateUtil from "@/utils/DateUtil";
import LoginHelper from "@/utils/LoginHelper";
import AjaxUtil from "@/utils/AjaxUtil";
import StringUtil from "@/utils/StringUtil";

import formConfig from "@/config/uploadLog-config";
import indexConfig from "@/config/index-config";
import echarts from "echarts";

export default {
  name: "page_averageMedianStat",
  
  data() {
    return {
      user: {
        roles: [],
        canStat: false,
        canEdit: false
      },
      tabs: {
        selfCityType: "primary",
        selfProvType: "",
        selfCityDisabled: false,
        selfProvDisabled: false
      },
      total: 0,
      currentPage: 1,
      pageSize: 20,
      averMedianData: [],
      tableAverMedData: [],
      groupPercentData: [],
      tableGroupPercentData: [],
      loading: false,
      formDisabled: true,

      cols: [],
      AverMedianDataAction:
        global.cmis.domain + "/carePlatform/index/getIndexAverageMedian",
      GroupPercentAction:
        global.cmis.domain + "/carePlatform/index/getGroupPercent",
      form: {
        value_idxName: "全员床护比",
        isCollapse:true,
        visible: false,
        groupVisible: false,
        //menu_index: indexConfig["groupIdx"],
        menu_index: indexConfig["idxMenu"],
        menu_map: indexConfig["idx"],
        menu_group: indexConfig["pgIdx"],
        combo_fromYear: formConfig.combo_year,
        combo_fromQuarter: formConfig.combo_quarter,
        combo_toYear: formConfig.combo_year,
        combo_toQuarter: formConfig.combo_quarter,
        combo_area: formConfig.combo_areaHierarchical,
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_orgGrade: formConfig.combo_orgGrade,
        combo_medinstCategory: formConfig.combo_medinstCategory,
        combo_orgProp: formConfig.combo_orgProp,
        combo_orgAttachType: formConfig.combo_orgAttachType,
        combo_BedNumberScope: formConfig.combo_BedNumberScope,
        combo_clinicaledutype: formConfig.combo_clinicaledutype,
        combo_orgId: formConfig.combo_orgIdHierarchical,
        // combo_orgId: (function() {
        //   var tmp = formConfig.combo_orgId;
        //   return tmp.sort(function(a, b) {
        //     return a.value - b.value;
        //   });
        // })(),

        // 默认6个季度前
        value_fromYear:
          DateUtil.dateAdd("q", -6, new Date()).getFullYear() + "",
        value_fromQuarter:
          parseInt(DateUtil.dateAdd("q", -6, new Date()).getMonth() / 3) +
          1 +
          "",

        value_toYear: new Date().getFullYear() + "",
        value_toQuarter:
          parseInt(DateUtil.dateAdd("q", -1, new Date()).getMonth() / 3) +
          1 +
          "",

        value_orgId: [], //单位
        value_area: formConfig.value_user.org_area,
        value_areaType: 1, //本市 1 本省 0
        value_yesno: "", //是否上报国家
        value_orgGrade: "", //单位定级
        value_orgProp: "", //所有制性质
        value_orgPartLable: "", //科室病区
        value_orgPartId: [formConfig.value_user.hasOwnProperty("orgPart_id")?formConfig.value_user.orgPart_id:""], //科室病区
        value_reportTemplate:formConfig.value_user.hasOwnProperty("orgPart_id")?formConfig.value_user.orgPart_template_id:1,
        value_medinstCategory: "", //单位类型
        value_orgAttachType: "", //隶属类型
        value_BedNumberScope: "", //床位数
        value_clinicaledutype: "", //临床教学基地类型
        value_indexId: "13", //指标,缺省全院床护比
        value_groupIdx: "", //分组指标
        value_hospital: ""
      }
    };
  },

  mounted: function() {
    var self = this;
    self.form.value_idxName = "全院床护比";
    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    } else {
      self.form.value_orgId = [formConfig.value_user.org_id];
      self.form.value_orgGrade = formConfig.value_user.org_grade;
      //value_hospital = formConfig.value_user.org_name;
    }

    //var areaId = formConfig.value_areaId[self.form.value_orgId].areaId;

    //参数
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);

    for (var idx in formValues) {
      self.form[idx] = formValues[idx];
    }

    var  idxId = self.form.value_indexId;
    if(self.form.menu_map.hasOwnProperty(idxId))
      self.form.value_groupIdx = self.form.menu_map[idxId]["partsGroup"];

    if (self.form.value_groupIdx == null) {
      self.form.groupVisible = false;
    } else {
      self.form.groupVisible = true;
    }

    
    // +
    // "--" +
    // self.form.value_hospital;
    self.getAverMedianData();

    //self.getDetailTableData();

    self.setupUserPermission();
  },

  methods: {
    handleRowClick(row, event, column) {
      var self = this;
      //timeStr 2019年1季度
      var regExp = /[0-9]+/g;
      var timeObj = row.timeStr.match(regExp);
      if (timeObj == null || timeObj.length != 2) {
        self.$alert("时间[" + row.timeStr + "]不正确!");
        return;
      }
      var fromDate = new Date(timeObj[0], (timeObj[1] - 1) * 3, 1);
      var fromStr = DateUtil.dateToStr(fromDate, "yyyy-MM-dd");
      var toDate = new Date(timeObj[0], timeObj[1] * 3, 1);
      var toStr = DateUtil.dateToStr(toDate, "yyyy-MM-dd");
      //console.log("lftest>>>>>" + fromStr + "," + toStr);
      var area;
      if (self.form.value_area.length == 0) {
      } else {
        area = self.form.value_area[0];
        if (self.form.value_areaType == 0 && self.form.value_area.length >= 2)
          area = self.form.value_area[1];
      }

      this.$router.push({
        name: "指标数据查询",
        params: {
          fromTime: fromStr,
          toTime: fromStr,
          orgPartId: this.form.value_orgPartId + "",
          area: area,
          orgId: self.form.value_orgId
        }
      });
      //query:{orgPartId:"1",area:'2080'}});
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getAverMedianData();
    },

    getSelfCity() {
      this.form.value_areaType = 1;
      this.tabs.selfCityType = "primary";
      this.tabs.selfProvType = "";
      this.getAverMedianData();
    },

    getSelfProvince() {
      this.form.value_areaType = 0;
      this.tabs.selfCityType = "";
      this.tabs.selfProvType = "primary";
      this.getAverMedianData();
    },

    changeOrgpart:function(labels) {
      var self=this;

      if(labels.length<=0)
      return;

      var selectId = labels[labels.length-1];
      self.form.value_reportTemplate = formConfig.value_orgPartId[selectId].templateId;  
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getAverMedianData();
    },
    handleOpen(key, keyPath) {
      //console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      //console.log(key, keyPath);
    },
    handleSelect(key, keyPath) {
      //console.log(key);
      //console.log(keyPath);
      //console.log(this.form.menu_map[key]["dictLabel"]);
      this.form.value_idxName = this.form.menu_map[key]["dictLabel"];
      // +
      // "--" +
      // this.form.value_hospital;
      this.form.value_indexId = this.form.menu_map[key]["id"];
      this.form.value_groupIdx = this.form.menu_map[key]["partsGroup"];
      if (this.form.value_groupIdx == null) {
        this.form.groupVisible = false;
      } else {
        this.form.groupVisible = true;
      }
      this.getAverMedianData();
    },

    getArea: function() {
      var self = this;
      if (self.form.value_area.length == 0) return;

      if (self.form.value_areaType == 1) {
        if (self.form.value_area.length >= 2)
          //return formConfig.value_areaId[self.form.value_area[1]].treeNames;
          return StringUtil.getTreeNames(formConfig.value_areaId,self.form.value_area[1]);
      }

      //return formConfig.value_areaId[self.form.value_area[0]].treeNames;
      return StringUtil.getTreeNames(formConfig.value_areaId,self.form.value_area[0]);
    },
    //请求季度统计值
    getAverMedianData: function() {
      var self = this;
      //self.$alert(self.form.value_orgPartId[0]+","+self.form.value_orgPartId[1]+','+self.form.value_orgPartId[2]+','+self.form.value_orgPartId[3]);
      if (self.form.value_indexId == null) {
        self.$alert("未选择相应指标!请选择指标后，再进行查询!");
        return;
      }

      self.loading = true;
      //地区选择
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.AverMedianDataAction,
        data: {
          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_toYear,
          toQuarter: self.form.value_toQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          nationUploadId: self.form.value_yesno,
          bedNumberScopeId: self.form.value_BedNumberScope,
          areaType: self.form.value_areaType,
          medinstCategory: self.form.value_medinstCategory,
          orgProp: self.form.value_orgProp,
          orgGrade: self.form.value_orgGrade,
          orgAttachType: self.form.value_orgAttachType,
          clinicaledutype: self.form.value_clinicaledutype,
          indexId: self.form.value_indexId,
          id: StringUtil.getLastNode(self.form.value_orgId),
          city: self.getArea()
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.averMedianData = res;
          self.total = res.count;
          self.getTableData();
          self.drawChart();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);

      if (self.form.menu_group.hasOwnProperty(self.form.value_groupIdx)) {
        self.form.groupVisible = true;
        self.getGroupPercentData();
      } else self.form.groupVisible = false;
    },
    getTableData() {
      var self = this;
      var len = self.averMedianData.listListEchartsData.length;
      var res = [];
      for (var i = 0; i < len; i++) {
        var objLen = self.averMedianData.listListEchartsData[i].length;

        res.push({
          timeStr: self.averMedianData.listListEchartsData[i][0],
          idxVal: self.averMedianData.listListEchartsData[i][1],
          departCnt: self.averMedianData.listListEchartsData[i][2],
          minVal: self.averMedianData.listListEchartsData[i][3],
          quantile10: self.averMedianData.listListEchartsData[i][4],
          quantile25: self.averMedianData.listListEchartsData[i][5],
          quantile50: self.averMedianData.listListEchartsData[i][6],
          quantile75: self.averMedianData.listListEchartsData[i][7],
          quantile90: self.averMedianData.listListEchartsData[i][8],
          maxVal: self.averMedianData.listListEchartsData[i][9]
        });

        //res.push(tmp);
      }
      self.tableAverMedData = res;
      //console.log("lftest+++-->" + self.tableAverMedData);
    },
    //绘制季度分位数
    drawChart() {
      var self = this;
      //self.$alert("drawChart");

      // 基于准备好的dom，初始化echarts实例
      let statQuantileState = echarts.init(
        document.getElementById("statQuantileState")
      );
      //console.log("tset1       " + self.averMedianData.listListEchartsData);
      //console.log("tset2       " + self.averMedianData.listEchartsSeries);
      // 绘制图表
      statQuantileState.setOption({
        legend: { y: "bottom" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },
        grid: {
          top: "3%", //距离上边的距离
          bottom: "12%",
          left: "3%",
          right: "3%",
          containLabel: true
        },
        dataset: {
          //source: self.tableAverMedData.listListEchartsData,
          source: (function() {
            var res = [];
            var len = self.averMedianData.listListEchartsData.length;
            for (var i = 0; i < len; i++) {
              var tmp = [];
              var objLen = self.averMedianData.listListEchartsData[i].length;
              for (var j = 0; j < objLen; j++) {
                if (j == 2 || j == 3 || j == 9)
                  //i=0为坐标,数量，最小值，最大值不显示
                  continue;
                tmp.push(self.averMedianData.listListEchartsData[i][j]);
              }
              res.push(tmp);
            }
            return res;
          })()
        },
        xAxis: {
          type: "category"
          // data: ["2019年第1季度", "2019年第2季度", "2019年第3季度"]
        },
        yAxis: {},
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        //series: self.tableAverMedData.listEchartsSeries,
        series: (function() {
          var res = [];
          var len = self.averMedianData.listEchartsSeries.length;
          for (var i = 0; i < len; i++) {
            if (i == 1 || i == 2 || i == 8)
              //数量，最小值，最大值不显示
              continue;

            // if(i==0)
            // {
            //   self.averMedianData.listEchartsSeries[i].set("barMaxWidth",20);
            // }
            self.averMedianData.listEchartsSeries[i].barMaxWidth = 20;
            res.push(self.averMedianData.listEchartsSeries[i]);
          }
          return res;
        })()
        // series: [
        //   {
        //     type: "bar",
        //     barMaxWidth: 20,
        //     name: "指标值",
        //     data: [0.25, 0.26, 0.25]
        //   },
        //   { type: "line", name: "10%分位数", data: [0.24, 0.27, 0.23] },
        //   { type: "line", name: "25%分位数", data: [0.23, 0.28, 0.21] },
        //   { type: "line", name: "50%分位数", data: [0.22, 0.29, 0.27] },
        //   { type: "line", name: "75%分位数", data: [0.21, 0.3, 0.29] },
        //   { type: "line", name: "90%分位数", data: [0.2, 0.31, 0.31] }
        // ]
      },true);
    },
    //占比统计
    getGroupPercentData: function() {
      var self = this;

      self.loading = true;
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: { withCredentials: true }, //表示发起跨域访问并要求携带Cookie等认证信息
        type: "post",
        url: self.GroupPercentAction,
        data: {
          fromYear: self.form.value_fromYear,
          fromQuarter: self.form.value_fromQuarter,
          toYear: self.form.value_toYear,
          toQuarter: self.form.value_toQuarter,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          templateId:self.form.value_reportTemplate,
          nationUploadId: self.form.value_yesno,
          bedNumberScopeId: self.form.value_BedNumberScope,
          areaType: self.form.value_areaType,
          medinstCategory: self.form.value_medinstCategory,
          orgProp: self.form.value_orgProp,
          orgGrade: self.form.value_orgGrade,
          orgAttachType: self.form.value_orgAttachType,
          clinicaledutype: self.form.value_clinicaledutype,
          indexId: self.form.value_indexId,
          groupIdx: self.form.value_groupIdx,
          id: StringUtil.getLastNode(self.form.value_orgId),
          city: self.getArea()
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;
          self.groupPercentData = res;
          self.total = res.count;
          self.getGroupPercentTable();
          self.drawPercentChart();
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };
      AjaxUtil.send(ajaxOpts);
    },

    getGroupPercentTable() {
      var self = this;
      var len = self.groupPercentData.listEchartsSeries.length;
      var rowLen = self.groupPercentData.listListEchartsData.length;
      var res = [];
      var cols = [];
      var charts = self.groupPercentData.listEchartsSeries;
      cols.push({ label: "季度", prop: "val0" });
      for (var i = 0; i < len; i++) {
        var idx = i + 1;
        cols.push({ label: charts[i].name, prop: "val" + idx });
      }
      self.cols = cols;
      for (var i = 0; i < rowLen; i++) {
        var tmp = {};
        var colLen = self.groupPercentData.listListEchartsData[i].length;
        for (var j = 0; j < colLen; j++) {
          tmp["val" + j] = self.groupPercentData.listListEchartsData[i][j];
        }
        res.push(tmp);

        //console.log("lftest+++" + tmp);
      }
      self.tableGroupPercentData = res;
    },
    //绘制占比统计
    drawPercentChart() {
      var self = this;
      // 基于准备好的dom，初始化echarts实例
      let statIndexPercent = echarts.init(
        document.getElementById("statPercentChart")
      );
      // 绘制图表
      statIndexPercent.setOption({
        legend: { y: "bottom" },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },
        grid: {
          top: "3%", //距离上边的距离
          bottom: "12%",
          left: "3%",
          right: "3%",
          containLabel: true
        },
        dataset: {
          //source: self.groupPercentData.listListEchartsData
          source: (function() {
            var res = [];
            //倒序，主要让时间最近的图像显示最上方
            self.groupPercentData.listListEchartsData.reverse();
            var len = self.groupPercentData.listListEchartsData.length;
            for (var i = 0; i < len; i++) {
              var tmp = [];
              var objLen = self.groupPercentData.listListEchartsData[i].length;
              for (var j = 0; j < objLen; j++) {
                if (j == 1)
                  //j 0为坐标,1类别,不显示
                  continue;
                if (j == 0)
                  tmp.push(
                    self.groupPercentData.listListEchartsData[i][0] +
                      "(" +
                      self.groupPercentData.listListEchartsData[i][1] +
                      ")"
                  );
                else tmp.push(self.groupPercentData.listListEchartsData[i][j]);
              }
              res.push(tmp);
            }
            return res;
          })()
        },
        xAxis: {
          type: "value"
        },
        yAxis: {
          type: "category"
        },
        // Declare several bar series, each will be mapped
        // to a column of dataset.source by default.
        //series: self.cityTableData.listEchartsSeries
        series: (function() {
          var res = [];
          var len = self.groupPercentData.listEchartsSeries.length;
          for (var i = 0; i < len; i++) {
            //类别不显示
            if (i == 0) continue;
            var tmp = self.groupPercentData.listEchartsSeries[i];
            tmp.stack = "percent";
            res.push(tmp);
          }
          return res;
        })()
        // series: [
        //   {
        //     type: "bar",barMaxWidth: 20,stack: '占比',name: "护士占比",data: [0.25, 0.26, 0.25,0.25, 0.26, 0.25]
        //   },
        //   { type: "bar",barMaxWidth: 20,stack: '占比',name: "护师占比", data: [0.24, 0.27, 0.23,0.25, 0.26, 0.25] },
        //   { type: "bar",barMaxWidth: 20,stack: '占比',name: "主管护师占比", data: [0.23, 0.28, 0.21,0.25, 0.26, 0.25] },
        //   { type: "bar",barMaxWidth: 20,stack: '占比',name: "副主任护师占比", data: [0.22, 0.29, 0.27,0.25, 0.26, 0.25] },
        //   { type: "bar",barMaxWidth: 20,stack: '占比',name: "主任护师占比", data: [0.21, 0.3, 0.29,0.25, 0.26, 0.25] },
        // ]
      },true);
    },

    /**
     * 用户权限: 管理员|单位人员
     */
    setupUserPermission: function() {
      var self = this;

      var userInfo = LoginHelper.logincheck(),
        usercode = userInfo ? userInfo.usercode : null;
      //权限, 临时
      //self.user.canStat = usercode && /^(system|bak[0-9]+)$/.test(usercode);
      //self.user.canEdit = usercode && /^(cdr[0-9]+)$/.test(usercode);
      var ajaxOpts = {
        async: false,
        url: global.cmis.domain + "/carePlatform/index/checkRights",
        type: "post",
        dataType: "json",
        data: { code: usercode },
        success: function(res) {
          if (res) {
            self.user.canStat = !!res.canStat;
            self.user.canEdit = !!res.canEdit;
          }
        }
      };
      AjaxUtil.send(ajaxOpts);
    },
    /**
     * 隐藏/显示查询条件
     */
    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    }
  }
};
</script>

