<template>
  <div>
    <el-container>
      <el-main class="data-body">
        <div id="pageHeader">
          <el-row class="pane-head">
            <el-col :span="21" class="pane-head-label">
              <i class="el-icon-menu"></i>&nbsp;中位数查询
            </el-col>
            <el-col :span="3">
              <el-button size="mini" @click="swapFormVisibility">{{form.visible? '隐藏' : '展开'}}查询条件</el-button>
            </el-col>
          </el-row>
        </div>

        <div id="searchFormPane" :style="{display: form.visible? '' : 'none'}">
          <el-form :inline="true" class="search-form-inline" :rule="form.rules">
            <el-row class="form-row">
              <el-col :span="21">
                <el-form-item label="地区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_area"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_area"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="formDisabled"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="科室病区" class="form-h-item">
                  <el-cascader
                    v-model="form.value_orgPartId"
                    placeholder="试试搜索"
                    size="mini"
                    :options="form.combo_orgPartId"
                    filterable
                    change-on-select
                    :show-all-levels="false"
                    :disabled="partUserFormDisabled"
                  ></el-cascader>
                </el-form-item>

                <el-form-item label="开始季度" class="form-h-item">
                  <el-select v-model="form.value_fromTime" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="截止季度" class="form-h-item">
                  <el-select v-model="form.value_toTime" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_quarterHeadDates"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="数据类型" class="form-h-item">
                  <el-select v-model="form.value_valueType" placeholder="全部" size="mini">
                    <el-option
                      v-for="item in form.combo_valueType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <!-- <el-form-item label="上报国家" class="form-h-item">
                <el-select v-model="form.value_nationalUpload" placeholder="全部" size="mini">
                  <el-option
                    v-for="item in form.combo_yesno"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                </el-form-item>-->
              </el-col>
              <el-col :span="3">
                <el-form-item class="form-h-item">
                  <el-button type="primary" size="mini" @click="getTableData">查询</el-button>
                  <el-button size="mini">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <div class="content-pane">
          <el-table
            :data="tableData"
            class="tb-edit"
            style="width: 100%"
            highlight-current-row
            stripe
            border
            size="mini"
            v-loading="loading"
            :height="dataTableHeight" ref="dataTable"
          >
            <el-table-column type="index" width="50" fixed/>
            <template v-for="(col, key, index) in cols">
              <el-table-column
                :prop="col.prop"
                sortable
                :label="col.label"
                :width="col.width || 90"
                v-bind:key="index"
                :fixed="col.fixed"
              >
                <template slot-scope="scope">
                  <el-tag type="primary" v-if="col.type==='sort'">{{ scope.row.type }}</el-tag>
                  <i
                    :class="{'el-icon-check': 'currentDataIsUpload' == col.prop && scope.row.firstDataQuarter && scope.row.firstDataQuarter == scope.row.currentDataQuarter}"
                  ></i>
                  <span>{{ scope.row[col.prop] }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>

          <div id="dataTablePager" class="paginationClass">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import globalVariables from "@/config/global-config";

import DateUtil from "@/utils/DateUtil";
import AjaxUtil from "@/utils/AjaxUtil";
import PageUtil from "@/utils/PageUtil";
import StringUtil from "@/utils/StringUtil";

import formHelper from "@/utils/formHelper";
import formConfig from "@/config/uploadLog-config";

export default {
  name: "page_qcIndexValue",
  
  data() {
    return {
      total: 0,
      currentPage: 1,
      pageSize: 20,
      tableData: [],
      loading: false,
      formDisabled: true,
      partUserFormDisabled: true,
      tableDataAction:
        globalVariables.cmis.domain + "/carePlatform/qcIndex/areaAvgValue",
      dataTableHeight: "100%",
      form: {
        visible: PageUtil.searchFormVisibility(),
        combo_submitState: formConfig.combo_submitState,
        combo_orgGrade: formConfig.combo_orgGrade,
        combo_yesno: formConfig.combo_yesno,
        combo_checkState: formConfig.combo_checkState,
        combo_area: formConfig.combo_areaHierarchical,
        combo_quarterHeadDates: formHelper.quarterHeadDates(2016),
        combo_orgPartId: formConfig.combo_orgPartIdHierarchical,
        //combo_orgPartId: formConfig.combo_orgPartId,
        // combo_orgPartId: (function() {
        //   var tmp = formConfig.combo_orgPartId.slice(0);
        //   if (tmp[0].label == "所有") tmp.shift();
        //   return tmp;
        // })(),
        combo_valueType: formConfig.combo_valueType,

        value_fromTime: "",
        value_toTime: "",
        value_submitState: "",
        value_valueType: "",
        value_nationalUpload: "",
        value_checkState: "",
        value_orgPartId: ["1"],
        value_area: formConfig.value_user.org_area
      },
      cols: [
        {
          label: "分析地区",
          prop: "area",
          type: "normal",
          width: 160,
          fixed: true
        },
        {
          label: "数据类型",
          prop: "valueTypeName",
          type: "normal",
          width: 100,
          fixed: true
        },
        {
          label: "分析时间",
          prop: "currentDataQuarter",
          type: "normal",
          width: 160,
          fixed: true
        }
      ],
      tableData: []
    };
  },

  mounted: function() {
    var self = this;

    self.layoutPage();

    if (1 == formConfig.value_user.org_id) {
      self.formDisabled = false;
    }

    var now = DateUtil.dateAdd("q", -1, new Date());
    //
    //self.form.value_fromTime = now.getFullYear() + "-01-01";
    var from = DateUtil.dateAdd("q", -4, now);
    self.form.value_fromTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(from),
      "yyyy-MM-dd"
    );

    self.form.value_toTime = DateUtil.dateToStr(
      DateUtil.quarterHeadDate(now),
      "yyyy-MM-dd"
    );

    //初始化 单位、病区
    self.form["value_orgPartId"] = StringUtil.getIdsById(
      formConfig.value_orgPartId,
      formConfig.value_user.orgPart_id
    );

    //如果是医院管理员manager 就可以编辑病区
    if (1 == formConfig.value_user.orgPart_id) {
      self.partUserFormDisabled = false;
    }

    //参数
    var query = (params = self.$route.query),
      params = (params = self.$route.params);
    var formValues = {};
    $.extend(formValues, query, params);
    //
    var k, v;
    for (k in formValues) {
      v = formValues[k];
      if (!v) {
        continue;
      }
      if ("area" == k) {
        v = v.split(",");
      }
      self.form["value_" + k] = v;
    }

    //地区只显示到 地市级以上
    if (self.form["value_area"].length > 2) {
      self.form["value_area"] = self.form["value_area"].slice(0, 2);
    }

    this.getTableData();
  },

  methods: {
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getTableData();
    },

    handleCurrentChange(currentPage) {
      this.currentPage = currentPage;
      this.getTableData();
    },

    getTableData: function() {
      var self = this;

      self.loading = true;

      var areasId = self.form.value_area;

      var numColsBase = 3;
      var cols = self.cols.slice(0, numColsBase);
      self.cols = cols;
      var lastNode = StringUtil.getLastNode(self.form.value_area);
      //请求数据
      var ajaxOpts = {
        acrossDomian: true,
        xhrFields: {
          withCredentials: true //表示发起跨域访问并要求携带Cookie等认证信息
        },
        type: "post",
        url: self.tableDataAction,
        data: {
          pageNo: self.currentPage,
          pageSize: self.pageSize,

          itemTypeName: "指标数据",
          //orgId: self.form.value_enteredAt,
          orgPartId: StringUtil.getLastNode(self.form.value_orgPartId),
          "quarterRange.fromTime": self.form.value_fromTime,
          "quarterRange.toTime": self.form.value_toTime,
          //, "submitState": self.form.value_submitState
          //, "checkState": self.form.value_checkState
          // area: areasId && areasId.length ? areasId[areasId.length - 1] : null,
          area: StringUtil.getTreeNames(formConfig.value_areaId, lastNode),
          // area:
          //   formConfig.value_areaId[
          //     StringUtil.getLastNode(self.form.value_area)
          //   ].treeNames,
          valueType: self.form.value_valueType
          //, "orgPart.extend.extendI1": self.form.value_nationalUpload
        },
        dataType: "json",
        success: function(res) {
          self.loading = false;

          //指标项 > 列
          var idxItems = res["otherData"] ? res["otherData"]["items"] : null;
          if (!idxItems) {
            return;
          }
          var re = /\./;
          for (var j = 0, itm, lbl, prop, iprop; j < idxItems.length; j++) {
            itm = idxItems[j];
            if (!re.test(itm["dictValue"])) {
              //过滤掉指标列表中code不含'.'的指标
              //console.log(itm);
              continue;
            }
            //
            lbl = itm["dictLabel"];
            prop = "val" + itm["id"];
            cols.push({ label: lbl, prop: prop, type: "normal", width: 180 });
          }

          self.tableData = res.list;
          self.total = res.count;
          
          setTimeout(function() {
            self.$refs.dataTable && self.$refs.dataTable.doLayout();
          }, 0);
        },
        error: function() {
          self.loading = false;

          self.$alert("加载失败");
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    onSearch: function() {
      this.getTableData();
    },

    swapFormVisibility: function() {
      this.form.visible = !this.form.visible;
    },

    layoutPage: function() {
      var self = this;

      setTimeout(() => {
        this.dataTableHeight = PageUtil.dataTableHeight();
      }, 0);
    }
  }
};
</script>

