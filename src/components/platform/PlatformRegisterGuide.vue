<template>
  <div class="platform-content-wrap platform-register">
    <div class="platform-register-breadcrumb">
      <i class="el-icon-warning"></i> 申请须知
    </div>

    <div class="platform-register-content">
      <vRegisterSteps :active="1" />

      <!-- <div>
        <el-steps :active="1" simple>
          <el-step title="申请须知" icon="el-icon-warning-outline"></el-step>
          <el-step title="医院信息填写" icon="el-icon-edit"></el-step>
          <el-step title="数据试填" icon="el-icon-user"></el-step>
        </el-steps>
      </div>-->

      <div class="platform-register-content-guide">
        <div>
          <h4>
            一、申请加入广西护理质量数据平台流程（
            <i style="color:grid;">特别提醒：申请加入要一次性完成，任意环节退出均需重新开始</i>）
          </h4>
          <div>
            <el-steps :active="4" align-center>
              <el-step title="材料准备" icon="el-icon-s-management" description>
                <template slot="description">
                  <div style="text-align:left;color: #fff;background-color:#4794e6;height:120px;">
                    填写医院信息
                    <br />1.下载申请承诺书，填写信息并加盖医院公章
                    <br />2.预先准备任意一季度数据（下载模版，查看收集变量）
                  </div>
                </template>
              </el-step>
              <el-step title="申请加入" icon="el-icon-s-promotion" description>
                <template slot="description">
                  <div style="text-align:left;color: #fff;background-color:#4794e6;height:120px;">
                    申请时间：
                    <br />x月x日-x月x日
                  </div>
                </template>
              </el-step>
              <el-step title="填写信息" icon="el-icon-s-custom">
                <template slot="description">
                  <div style="text-align:left;color: #fff;background-color:#4794e6;height:120px;">
                    填写医院信息
                    <br />1.上传申请承诺书扫描件
                    <br />2.完善医院相关信息
                  </div>
                </template>
              </el-step>
              <el-step title="等待审核" icon="el-icon-s-check">
                <template slot="description">
                  <div style="text-align:left;color: #fff;background-color:#4794e6;height:120px;">
                    等待省级质控中心管理员审核
                    <br />1.审核时间：x月x日-x月x日
                    <br />2.审核通过后，系统短信发送账号和密码至医院管理员预留的手机号。医院用户开始填报数据。
                  </div>
                </template>
              </el-step>
            </el-steps>
          </div>
          <h4>
            <br />二、点击下载承诺书
          </h4>
          <a href="static/template/填报模板.rar">1.填报模板.rar</a>
          <br />
          <a href="static/template/加入广西护理质量数据平台申请书模板.docx">2.申请加入承诺书.docx</a>
          <h4>
            <br />三、用户须知
          </h4>
          <div>
            <p>
              1.数据填报制度
              <br />（1）指标变量数据，按季度填报，填报时限为第一季度4月1日-5月10日，第二季度7月1日-8月10日，第三季度10月1日-11月10日，第四季度1月1日-2月10日；如未能按时提交数据，账号自动冻结。
              <br />（2）护士执业环境测评，一年测评一次，每年中下旬进行。由省级平台管理员发起，医院管理员组织，要求医院符合条件的护士参与率超过60%。
              <br />（3）时点调查，一年测评一次，每年中下旬进行。由省级平台管理员发起，医院管理员组织，要求全院病区参与。
              <br />
            </p>
            <p>
              2.数据修改制度
              <br />（1）数据修改申请在线提交时限：第一季度4月1日-5月30日，第二季度7月1日-8月30日，第三季度10月1日-11月30日，第四季度1月1日-2月28日。超时无法提交。
              <br />（2）数据修改申请由省级质控中心管理员审核判断是否同意。
              <br />
            </p>
            <p style="color:red;margin-left:2px;">
              <b>
                <br />四、温馨提示：IE10及以下版本浏览器会影响您的体验，请及时更新您的浏览器，建议使用谷歌浏览器。
              </b>
            </p>
            <p style="color:red;margin-left:2px;">
              <b>点击下载谷歌浏览器：</b>
              <a href="static/install/谷歌浏览器安装包.exe">1.谷歌浏览器安装包</a>
              <a href="static/install/谷歌浏览器免安装绿色版.rar">2.谷歌浏览器免安装绿色版</a>
            </p>
          </div>
        </div>

        <div class="platform-register-content-bar">
          <el-row>
            <el-col :span="20">
              <div class>
                <el-checkbox v-model="isAgree" @change="changeAgree">我已盖章申请书，同意并已仔细阅读上述须知</el-checkbox>
              </div>
            </el-col>
            <el-col :span="4">
              <div class>
                <el-button
                  :type="approved"
                  :disabled="!isAgree"
                  @click="goto({name: '用户信息填写'})"
                >申请加入</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PlatformRegisterSteps from "@/components/platform/PlatformRegisterSteps.vue";

import formConfig from "@/config/uploadLog-config";
formConfig.init(true);

export default {
  components: {
    vRegisterSteps: PlatformRegisterSteps
  },

  data() {
    return {
      isAgree: false,
      approved: "info",

      tableData: [
        {
          idxName: "压疮高危患者压疮发生率",
          gx: "X",
          structure: "",
          process: "",
          result: ""
        },
        {
          idxName: "患者深静脉血栓发生率",
          gx: "X",
          structure: "",
          process: "",
          result: ""
        },
        {
          idxName: "床护比",
          gx: "",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护患比",
          gx: "",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "每住院患者24小时平均护理时数",
          gx: "",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "不同级别护士配置",
          gx: "",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护士离职率",
          gx: "",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护士执业环境",
          gx: "",
          structure: "X",
          process: "",
          result: "X"
        },
        {
          idxName: "住院患者身体约束率",
          gx: "",
          structure: "",
          process: "X",
          result: ""
        },
        {
          idxName: "住院患者跌倒发生率",
          gx: "",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "住院患者压力性损伤发生率",
          gx: "",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "住院患者非计划拔管发生率",
          gx: "",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "中心血管导管相关血流感染发生率",
          gx: "",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "呼吸机相关肺炎发生率",
          gx: "",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "导尿管相关尿路感染发生率",
          gx: "",
          structure: "",
          process: "",
          result: "X"
        }
      ]
    };
  },

  methods: {
    changeAgree() {
      var self = this;
      if (self.isAgree) {
        self.approved = "primary";
      } else {
        self.approved = "info";
      }
    },
    downLoad() {
      window.open("static/template/填报模板.rar");
    },

    goto: function(route) {
      this.$router.push(route);
    }
  }
};
</script>
