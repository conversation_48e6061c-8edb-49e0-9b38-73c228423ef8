<template>
  <div class="platform-content-wrap platform-register">
    <div class="platform-register-breadcrumb">
      <i class="el-icon-office-building"></i> 单位信息
    </div>

    <div class="platform-register-content">
      <vRegisterSteps :active="3" />
      
      <vUserOrganization editMode="register" ref="uo" />

      <div class="platform-register-content-bar platform-register-content-bar-buttons">
        <el-button type="primary" @click="confirmTodo" :loading="processing" :disabled="!editable">保存并提交审核</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import model from "@/assets/js/platform/PlatformRegisterOrg";

export default model;

</script>
