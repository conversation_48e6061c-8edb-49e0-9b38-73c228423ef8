<template>
  <div class="platform-content-wrap platform-register">
    <div class="platform-register-breadcrumb">
      <i class="el-icon-user-solid"></i> 个人信息
    </div>

    <div class="platform-register-content">
      <vRegisterSteps :active="2" />

      <vUserInfo editMode="register" ref="ui" />

      <div class="platform-register-content-bar platform-register-content-bar-buttons">
        <el-button type="primary" @click="confirmTodo" :loading="processing">确认并进行下一步单位信息完善</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import model from "@/assets/js/platform/PlatformRegisterUser";

export default model;
</script>
