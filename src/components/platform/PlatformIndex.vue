<template>
  <div class="platform-index">
    <div class="platform-content-wrap platform-index-brief">
      <div class="platform-index-brief-header">
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAcYAAAAoCAMAAABThapdAAABKVBMVEUAAADMMzP/gIC6MDTEOzu+NDS6MTS6MDO6MTS7MjW5MTTINze/QEC6MTO6MTS6MTO6MDS8NTW5MTO6NTW5MDO6MDS7MzO6MTPDPDzVVVW5MTO5MTS5MTO5MDT///+7MzO6MDO8MDW5MTS6MTO8MTW5MDO/MzO/QEC5MTO5MDW5MjS6MDT/VVW6MTu5MDS6NDS5MTO5MTS5MTW5MTS/NTW6MTO6MTW6MDS6MDS8MTe7MDS6MTO5MjO6MDS9OTm6MjW7MzO8MjfCMT28Mja6MTS7MTW6MTS6MTO6MDS7NDS6MzPGOTm6MjS5MDTbSUm6MTS7MTO5MjS6MDW5MTO7MjPGOTm8Nja6MDO6MTW6MDS6MTO7MjS6MTS9MzO6MTS8Nja6MTO5MDO6MTPCwqnJAAAAY3RSTlMACgKJDSf2uY1S5w4I4F2CxCL1MLOUS+sRBtxjfMkBHvQ1rJtE7hQE12p1zwMa8juloT7xGIF+znoqf32LvxtgLS4VTOyDq6DTQEYSa9gHpmmAb4eVCROfQ6NybJwjvCaMvuE22Ke7AAABHUlEQVR4Ae3XA46kAQBE4Rq1bdu23Wur177/Hdbe+WN0JfUl7xAPwu/kFMLv7Bz05MJ0CnpitkDoWW12CDuH0wWh5/ZA6Hl9fgi9QBBCLxSOQNhFY3EIvUQyBWGXzmQh9HJ5CL1CsQRhV65UQU9q9QbYSbPVBj3pdEFPev0BjA1SwxHU8TeewFhimprNoY6/xRKGVpUyKMiJcw0Dm+0OJGR/pYRLXQ2VITSuXcelbkTBQ5r9m/jfrfPbYCJ37t7Dv+4/SIGKpJIP8S+zA2TEn7nAX9Y4Bx35Zx4fPQY/zeOTAxjJPvwUP81xACfJPcMPz1+An+bx5QG05NX3eXyNKHhJKjkD8OYtqInf9w6wOsBN3n8APzn5GAU/2Z9/BtZ5jrZtyzwrAAAAAElFTkSuQmCC"
          alt="虚线"
          style="width: 452px; vertical-align: text-bottom; padding: 0px 12px;"
        />
        <div class="platform-index-brief-header-label">
          <p>平台介绍</p>
          <p>Introduction</p>
        </div>
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAccAAAAoCAYAAACLmTFRAAACsklEQVR4Ae3dM/xtRxRH8Yltp4qN2bP3Off5xbadNHGaOFVsNzHb2Jh9LmKXsW2rve9/PufZ5vp2q7vV9W8mvDxs2GphHAAA4GJ3BwAAME6O9ndWbY1OAABQiV7gYm/3+/3FBhIAALyjupSLfpxTcVJoAACALLani/362uDBq4cGAADIYo9n0VtHJwAAyGW5YRb9rzKT0AAAADnZxVns1dEJAAB6O+ywbI76RY56fGgAAIAq2v4e7cdKdZXQAAAAWew5T3rz6AQAAB5bm2bR/7LZNgEAADSy6FUerTc6AQBA3m23FXK0b7LYEQEAADSqVBzmUb+tXygDAABo5Khdj3bN6AQAAG1pbeVi/3fNNg8AAKDhYjd41Dw6AQDAG63WylnshyoVBwUAANCo73us/73qope42LFhQLsoNqJpmqbp+a3nmiz2sIs+VEW7yFNxzJgHM781TdM0Tc8V7VTu42K/9lTXDAAAzMc6qsmjPt1Ore3m6GEALvolN3UAABawpcXt9XGoYU5w0etHHyMHAAC6Um7vUf9l4wgAWBC1VXf0aB+5lBZmh/7FFy+eo71Z/8AZAABYQI3+J+tZLkPXD7OqSnZGjvbhe4ceunQAAGABVr+WvSTD1+rtsMOSYWbVr65Z7K/642gAAGAh4NH2ctFfcmrprGwa7w8AACxEXlJdr54n9nbYYdUwIyrVvdk0AgAWVu2yXKMTiyG9HXZYcYY2jZ6KEwIAAAuptuoBHvWnepURpiUnvY5NIwBgUfDK0KEr1VPFT/bccxk2jQAAjJbLcsNK7LiXhw1bbYqbxpzs4rAIAQCgSsVhWeyHblFsHcbnyU73aB8tqptGAAD6/f5iE/ytlU3jAAAAt3mIXpqHDFk7eNQHXdg0AgBQqW7romcHF/1lvE0jAACY8J5GAAAwCvv5jFsBcxG4AAAAAElFTkSuQmCC"
          alt="虚线"
          style="width: 452px; vertical-align: text-bottom; padding: 0px 12px;"
        />
      </div>
      <div class="platform-index-brief-content">
        <p>广西护理质量数据平台，创建于2019年7月，由广西护理质量控制中心挂靠单位广西医科大学第一附属医院研发。</p>
        <p>平台业务管理部门为广西护理质量控制中心，平台工作是在广西壮族自治区卫生健康委医政医管处领导下，重点负责收集、分析全区护理质量数据，定期发布质控信息，促进质量持续改进，提高护理质量同质化水平，并指导区内各地市护理质控中心开展质控工作。</p>
        <p>平台主要数据模块为临床护理敏感指标、护理不良事件和优质护理服务工作开展覆盖率信息。</p>
        <!-- <p>截止到2018年底，拥有医院用户1533家，其中三级甲等医院849家，占全国三级医院的76.46%；教学医院1674家。平台涉及13项76个指标，95个数据变量。</p> -->
      </div>
    </div>

    <div class="platform-content-wrap platform-index-data-overview">
      <el-table
        :data="tableData"
        :row-class-name="tableRowClassName"
        style="margin:0 auto;width: 1014px"
        highlight-current-row
        stripe
        border
        size="mini"
      >
        <el-table-column prop="idxName" label="指标"></el-table-column>
        <el-table-column prop="structure" label="结构">
          <template slot-scope="scope">
            <i v-if="scope.row.structure=='X'" class="el-icon-star-on" style="color:red;"></i>
          </template>
        </el-table-column>
        <el-table-column prop="process" label="过程">
          <template slot-scope="scope">
            <i v-if="scope.row.process=='X'" class="el-icon-star-on" style="color:red;"></i>
          </template>
        </el-table-column>
        <el-table-column prop="result" label="结果">
          <template slot-scope="scope">
            <i v-if="scope.row.result=='X'" class="el-icon-star-on" style="color:red;"></i>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style type="text/css">
div.el-table .table-row-group td,
div.el-table--striped table.el-table__body tr.table-row-group td {
  color: #fff !important;
  background-color: #3a8ee6 !important;
}

div.el-table .table-row-group-gx td,
div.el-table--striped table.el-table__body tr.table-row-group-gx td {
  color: #fff !important;
  background-color: #00974c !important;
}
</style>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          idxName: "广西护理敏感质量指标",
          structure: "",
          process: "",
          result: ""
        },
        {
          idxName: "压力性损伤高危患者压力性损伤发生率",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "患者深静脉血栓发生率",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "国家护理敏感质量指标",
          structure: "",
          process: "",
          result: ""
        },
        {
          idxName: "床护比",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护患比",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "每住院患者24小时平均护理时数",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "不同级别护士配置",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护士离职率",
          structure: "X",
          process: "",
          result: ""
        },
        {
          idxName: "护士执业环境",
          structure: "X",
          process: "",
          result: "X"
        },
        {
          idxName: "住院患者身体约束率",
          structure: "",
          process: "X",
          result: ""
        },
        {
          idxName: "住院患者跌倒发生率",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "住院患者压力性损伤发生率",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "住院患者非计划拔管发生率",
          structure: "",
          process: "X",
          result: "X"
        },
        {
          idxName: "中心血管导管相关血流感染发生率",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "呼吸机相关肺炎发生率",
          structure: "",
          process: "",
          result: "X"
        },
        {
          idxName: "导尿管相关尿路感染发生率",
          structure: "",
          process: "",
          result: "X"
        }
      ]
    };
  },
  methods: {
    tableRowClassName: function(tr) {
      var row = tr["row"],
        code = row["code"];
      //编码没有"."的是第1级分类分组
      var cls = "";
      //广西护理敏感质量指标 就换一种背景色
      if (row["idxName"] && row["idxName"].indexOf("护理敏感质量指标") > -1) {
        cls = "table-row-group";
      }
      return cls;
    }
  }
};
</script>

