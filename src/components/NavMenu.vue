<template>
<div>
	<ul class="sidebar-menu" data-widget="tree">
		<!-- <li class="header">功能导航</li> -->
		
		<li v-for="(item, k, i) in menu" class="treeview" v-if="'0' != item.isShow" :key="i">
			<a href="javascript://"><i :class="'fa fa-link ' + item.icon"></i> <span>{{ item.name }}</span>
				<span v-if="item.sub && item.sub.length" class="pull-right-container">
				<i class="fa fa-angle-left pull-right"></i>
				</span>
			</a>
			
			<ul v-if="!!(item.sub && item.sub.length)" class="treeview-menu">
				<li v-for="(sub, k1, i1) in item.sub" v-if="'0' != sub.isShow" :key="i1">
					<!-- <a :href="'#/' + sub.componentName">{{ sub.name }}</a> -->
					<router-link :to="{ path: ('/' + sub.componentName), name: sub.name, params: sub.params }">
						<i class="el-icon-menu"></i>{{ sub.name }}</router-link>
				</li>		
			</ul>
		</li>		
	</ul>
</div>
</template>

<script>
  import menuConfig from '@/config/menu-config'
	
  export default {
    data () {
      return {
        menu: menuConfig
				//, currentRoute: window.location.pathname
      }
		},
		methods: {
			initTreeVeiw: function() {
				//展开第一个模块
				$('.sidebar-menu .treeview:first').addClass("menu-open");
				$('.sidebar-menu .treeview:first .treeview-menu').show();
			}

		},
		mounted: function() {
			var self = this;

			self.initTreeVeiw();
		}
		/*, computed: {
			ViewComponent () {
		  	return routes[this.currentRoute] || NotFound
			}
		}
		, render (h) { return h(this.ViewComponent) }*/
  }
</script>