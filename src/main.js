import Vue from 'vue'
import promise from 'es6-promise'
promise.polyfill();
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
//import 'normalize.css'
//import 'vue-awesome/icons'
//import Icon from 'vue-awesome/components/Icon'

import App from './App'
import router from './router'
import store from './store'

import 'bootstrap/dist/css/bootstrap.min.css'
import 'admin-lte/dist/css/AdminLTE.min.css'
import 'admin-lte/dist/css/skins/skin-blue.min.css'
import 'font-awesome/css/font-awesome.min.css'
import 'ionicons/dist/css/ionicons.min.css'
import 'bootstrap/dist/js/bootstrap.min'
import 'admin-lte/dist/js/adminlte.min'

Vue.config.productionTip = false

Vue.use(ElementUI)
//Vue.component('icon', Icon)

$.support.cors = true;

new Vue({
  el: '#app',
  router,
  store,
  /*template: '<App/>',*/
  components: { App },
  render: h => h(App)
})//.$mount('#app')
