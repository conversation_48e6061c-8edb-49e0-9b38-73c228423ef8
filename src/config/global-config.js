//
module.exports = {
  productName: "地贫治疗数据采集平台",
  loading: {
    mask: {
      lock: true,
      text: "正在处理....",
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)"
    }
  },
  sso: {
    domain: "http://" + location.hostname + ":8480/rh-sso-server"
  },
  cmis: {
    domain: "http://" + location.hostname + ":8880/rh-cmis"
  },
  platform: {
    indexUploadAction: "http://" + location.hostname + "/upload-index",
    indexValidatorAction: "http://" + location.hostname + ":8881/validate"
  },
  fileUpload: {
    // 可配置的文件上传端点
    domain: "http://***********:8088",
    endpoint: "/fileUpload"
  },
  userAvatar: "/static/images/user1.jpg",
  userAvatarWomen: "/static/images/user2.jpg",
  securityKey: "$,$$,$$$"
};
