//
import globalVariables from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";

var emptyComboItem = {
  value: "",
  label: "全部"
};
var formConfig = {
  combo_year: [emptyComboItem],
  combo_quarter: [emptyComboItem],
  combo_submitState: [emptyComboItem],
  combo_orgId: [emptyComboItem], //不再用
  combo_orgIdHierarchical: [emptyComboItem],
  combo_orgPartId: [emptyComboItem], //不再用
  combo_orgPartIdHierarchical: [emptyComboItem],
  combo_orgGrade: [emptyComboItem],
  combo_orgClass: [emptyComboItem],
  combo_medinstCategory: [emptyComboItem],
  combo_yesno: [emptyComboItem],
  combo_checkState: [emptyComboItem],
  combo_checkState_check: [emptyComboItem],
  combo_orgProp: [emptyComboItem],
  combo_area: [emptyComboItem], //不再用
  combo_areaHierarchical: [emptyComboItem],
  //combo_provice: [emptyComboItem],
  //combo_city: [emptyComboItem],
  combo_orgAttachType: [emptyComboItem],
  combo_valueType: [emptyComboItem],
  combo_BedNumberScope: [emptyComboItem],
  combo_clinicaledutype: [emptyComboItem],
  combo_jobType: [emptyComboItem],
  combo_reportTemplate: [emptyComboItem],
  combo_gender: [emptyComboItem],
  combo_ethnicGroup: [emptyComboItem],
  combo_orgPartByOrg: [],

  value_user: {},
  value_areaName: {},
  value_areaId: {},
  value_orgPartName: {},
  value_orgPartId: {},
  value_orgName: {},
  value_orgId: {},
  value_jobType: {},
  /**
   * 有序的二分查找，返回-1或存在的数组下标。不使用递归实现。
   * @param target
   * @param array
   * @returns {*}
   */
  getForm: function(array, target) {
    var start = 0;
    var end = array.length - 1;

    while (start <= end) {
      var mid = parseInt(start + (end - start) / 2);
      if (target == array[mid].value) {
        return mid;
      } else if (target > array[mid].value) {
        start = mid + 1;
      } else {
        end = mid - 1;
      }
    }
    return -1;
  },

  /**
   * 读取/更新字典
   */
  loadData: loadData,

  /**
   * 字典转为ElementUI节点
   */
  makeOptions: makeOptions,

  /**
   * 字典转为ElementUI节点
   */
  init: initFormConfig
};

/**
 * 是否已初始
 */
let isInited = false;
/**
 *
 */
function initFormConfig(async) {
  if (isInited) {
    return;
  }
  //年份
  var yr = new Date().getFullYear();
  var combo_year = [emptyComboItem];
  for (var y = yr; y >= 2016; y--) {
    combo_year.push({
      value: y,
      label: y
    });
  }
  formConfig.combo_year = combo_year;

  loadData(null, async);
  //标志已初始, 只执行一次, 忽略是否初始成功
  isInited = true;
}

function getOrgPartCombo(orgId) {
  var combo = [];
  if (orgId == null) {
    return combo;
  }
  var tmpOrgId = orgId;

  for (var id in formConfig.value_orgPartId) {
    if (formConfig.value_orgPartId[id].extendS2 === tmpOrgId) {
      combo.push({
        label: formConfig.value_orgPartId[id].treeNames,
        value: id
      });
    }
  }
  return combo;
}

/**
 * 读取字典
 * @param dictType 指定字典
 * @param async 是否异步
 */
function loadData(dictType, async) {
  //
  var url = globalVariables.cmis.domain + "/carePlatform/index/readFormCombo";
  if (dictType) {
    url += "?" + dictType;
  }
  var ajaxOpts = {
    async: !!async,
    url: url,
    dataType: "json",
    success: function(res) {
      //
      if (!res) {
        return;
      }
      //用户信息
      var user = res["user"];
      if (user) {
        formConfig["value_user"]["name"] = user["userName"];
        formConfig["value_user"]["mobile"] = user["mobile"];
      }
      //机构
      var userOrg = res["userOrg"];
      if (userOrg) {
        //机构
        formConfig["value_user"]["org_id"] = userOrg["id"];
        formConfig["value_user"]["org_tree_ids"] = userOrg["treeIds"]
          ? userOrg["treeIds"].split(",")
          : null;
        formConfig["value_user"]["org_name"] = userOrg["dictLabel"];
        //地区
        var userOrgEx = userOrg["extend"];
        if (userOrgEx) {
          if (userOrgEx["extendS1"]) {
            //org grade
            formConfig["value_user"]["org_grade"] = userOrgEx["extendS1"];
          }
          if (userOrgEx["extendS2"]) {
            //org class
            formConfig["value_user"]["org_class"] = userOrgEx["extendS2"];
          }
          if (userOrgEx["extendS3"]) {
            //机构类型
            formConfig["value_user"]["org_cat"] = userOrgEx["extendS3"];
          }
          if (userOrgEx["extendS4"]) {
            //所有制性质
            formConfig["value_user"]["org_prop"] = userOrgEx["extendS4"];
          }
          if (userOrgEx["extendF1"]) {
            //是否上报
            formConfig["value_user"]["org_upload"] = userOrgEx["extendF1"];
          }
          if (userOrgEx["extendI1"]) {
            //临床教学
            formConfig["value_user"]["org_edu"] = userOrgEx["extendI1"];
          }
          if (userOrgEx["extendI2"]) {
            //bed conut
            formConfig["value_user"]["org_bed"] = userOrgEx["extendI2"];
          }
          if (userOrgEx["extendI3"]) {
            //nurse conut
            formConfig["value_user"]["org_nurse"] = userOrgEx["extendI3"];
          }
          if (userOrgEx["extendI4"]) {
            //隶属类型
            formConfig["value_user"]["org_attach"] = userOrgEx["extendI4"];
          }
          if (userOrgEx["extendD1"]) {
            formConfig["value_user"]["org_pass_date"] = userOrgEx["extendD1"];
          }
          if (userOrgEx["extendS5"]) {
            formConfig["value_user"]["org_area"] = userOrgEx["extendS5"]
              ? userOrgEx["extendS5"].split(",")
              : null;
          }
        }
      }
      //科室病区
      var userOrgPart = res["userOrgPart"];
      if (userOrgPart) {
        formConfig["value_user"]["orgPart_id"] = userOrgPart["id"];
        formConfig["value_user"]["orgPart_tree_ids"] = userOrgPart["treeIds"]
          ? userOrgPart["treeIds"].split(",")
          : null;
        formConfig["value_user"]["orgPart_tree_names"] =
          userOrgPart["treeNames"];
        formConfig["value_user"]["orgPart_template_id"] =
          userOrgPart["extend"]["extendI4"];
      }
      //console.log(formConfig["value_user"]);

      //下拉选项数据
      for (var nam in res) {
        var fk = "combo_" + nam;
        if (fk in formConfig && nam in res) {
          var items = res[nam];
          /*if(items && items.length) {
            var formItems = formConfig[fk], formItem, formItemChildren;
            for(var i = 0, itm; i < items.length; i ++) {
              itm = items[i];

              formItems.push(formItem = {value: itm["id"], label: itm["dictLabel"]});
              //
            }
          }*/
          var formItems = makeOptions(items);
          formItems.unshift(emptyComboItem);
          formConfig[fk] = formItems;
          //
          if ("checkState" == nam) {
            var items_check = [];
            for (var j = 0; j < items.length; j++) {
              if (items[j]["extend"] && 1 == items[j]["extend"]["extendI1"]) {
                items_check.push(items[j]);
              }
            }
            formItems = makeOptions(items_check);
            formItems.unshift(emptyComboItem);
            formConfig["combo_checkState_check"] = formItems;
          }

          if ("area" == nam) {
            for (var j = 0; j < items.length; j++) {
              formConfig.value_areaId[items[j].id] = {
                id: items[j].id,
                dictLabel: items[j].dictLabel,
                parentCode: items[j].parentCode,
                parentCodes: items[j].parentCodes,
                treeNames: items[j].treeNames
              };
              formConfig.value_areaName[items[j].dictLabel] = {
                id: items[j].id
              };
            }
          }

          if ("orgPartId" == nam) {
            for (var j = 0; j < items.length; j++) {
              formConfig.value_orgPartId[items[j].id] = {
                id: items[j].id,
                dictLabel: items[j].dictLabel,
                parentCode: items[j].parentCode,
                parentCodes: items[j].parentCodes,
                treeNames: items[j].treeNames,
                extendS2: items[j].extend.extendS2,
                templateId: items[j].extend.extendI4 //科室对应的模板
              };
              formConfig.value_orgPartName[items[j].dictLabel] = {
                id: items[j].id
              };
            }
          }

          if ("orgId" == nam) {
            for (var j = 0; j < items.length; j++) {
              formConfig.value_orgId[items[j].id] = {
                id: items[j].id,
                dictLabel: items[j].dictLabel,
                parentCode: items[j].parentCode,
                parentCodes: items[j].parentCodes,
                treeNames: items[j].treeNames,
                areaId: items[j].extend.extendS5 //区域id
              };
              formConfig.value_orgName[items[j].dictLabel] = {
                id: items[j].id
              };
            }
          }

          if ("jobType" == nam) {
            for (var j = 0; j < items.length; j++) {
              formConfig.value_jobType[items[j].id] = {
                id: items[j].id,
                dictLabel: items[j].dictLabel,
                parentCode: items[j].parentCode,
                parentCodes: items[j].parentCodes,
                treeNames: items[j].treeNames,
                period: items[j].extend.extendI4 //数据周期id
              };
            }
          }
        }
      }
      //根据机构显示病区
      formConfig.combo_orgPartByOrg = getOrgPartCombo(
        formConfig.value_user.org_id
      );
    }
  };

  AjaxUtil.send(ajaxOpts, true);
}
/**
 *
 * @param {*} items
 */
function makeOptions(items) {
  var opts = [];

  if (items && items.length) {
    for (var j = 0, itm, opt, optChd; j < items.length; j++) {
      itm = items[j];
      opts.push(
        (opt = {
          value: itm["id"],
          label: itm["dictLabel"]
        })
      );
      //
      optChd = makeOptions(itm["children"]);
      if (optChd.length) {
        opt["children"] = optChd;
      }
    }
  }

  return opts;
}

//initFormConfig();

//module.exports = formConfig;
export default formConfig;
