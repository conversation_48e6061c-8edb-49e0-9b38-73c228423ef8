//
import globalVariables from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";

var indexConfig = {
};

/**
 * 
 */
function initIndexConfig() {

  var ajaxOpts = {
    async: false
    , url: globalVariables.cmis.domain + "/carePlatform/index/getIndexNurse"
    //, url: "http://127.0.0.1:8880/rh-cmis/carePlatform/index/getIndexNurse"
    , dataType: "json"
    , success: function(res) {
      if(!res) {
        return;
      }
      indexConfig = res;
      indexConfig.idxMenu = getMenu(res);
        }
      };

  AjaxUtil.send(ajaxOpts);
};

function getMenu(res) {
          
  var tmpIdx = res["groupIdx"]["指标数据"];
  var tmpGroup = indexConfig["groupIdx"];
  var len = tmpIdx.length;
  var groupMaps = {};
  //模板目前对应的
  //1 全院 
  //2 病区 
  //3 ICU
  var tmp = {1:[],2:[],3:[]};
  for(var idx=0;idx<len;idx++ )
  {
    var groupName = tmpIdx[idx]['dictLabel'];
    groupMaps[groupName]=idx;
    tmp["1"].push({});
    tmp["1"][idx][groupName]={};
    tmp["2"].push({});
    tmp["2"][idx][groupName]={};
    tmp["3"].push({});
    tmp["3"][idx][groupName]={};
  }
  for (var idx in tmpGroup) {
    if (idx == "指标数据") continue;
    if(!groupMaps.hasOwnProperty(idx)) continue;
    var groupIdx = groupMaps[idx];
    //过滤全院指标
    var tmpIdxs = tmpGroup[idx].filter(item=>item.templateIds.indexOf(1)>=0);
    tmp['1'][groupIdx]={name:idx,
                   idxVal:tmpIdxs};
    //过滤病区指标
    tmpIdxs = tmpGroup[idx].filter(item=>item.templateIds.indexOf(2)>=0);
    tmp['2'][groupIdx]={name:idx,
                   idxVal:tmpIdxs};
    //过滤ICU指标
    tmpIdxs = tmpGroup[idx].filter(item=>item.templateIds.indexOf(3)>=0);
    tmp['3'][groupIdx]={name:idx,
                   idxVal:tmpIdxs};
  }
  return tmp;
}

initIndexConfig();

export default indexConfig;