import global from '@/config/global-config'
	
import AjaxUtil from '@/utils/AjaxUtil'

//
var menus = [];

function findMenus() {

  menus = [];

  var ajaxOpts = {
    async: false
    , url: global.cmis.domain + "/carePlatform/menu/findMenu"
    , method: "post"
    , dataType: "json"
    , success: function(res) {
	  //console.log(res);
      if(!res || !res.length) {
        return;
      }
	    //一级菜单
	    var i, pitem, pmenu, j, sitem, smenu, smenuHref, smenuQuery, smenuParam, smenuPerm, k, bitems, bitem;
      for(i = 0; i < res.length; i ++) {
        pitem = res[i];
        menus.push(pmenu = {name: pitem["menuNameOrig"], id: pitem["menuCode"], icon: pitem["menuIcon"], isShow: pitem["isShow"], sub: []});
		    //二级菜单
        for(j = 0; j < pitem.childList.length; j ++) {
          sitem = pitem.childList[j];
		      //连接/页面文件名称
          smenuHref = sitem["menuHref"];
		      //参数
          smenuParam = {};
          var pos;
          if(smenuHref && -1 != (pos = smenuHref.indexOf("?"))) {
            smenuQuery = smenuHref.substring(pos + 1);
            smenuHref = smenuHref.substring(0, pos);
            var smenuQueryPairs = smenuQuery.split("&");
            for(var j1 = 0; j1 < smenuQueryPairs.length; j1 ++) {
              var smenuQueryPair = smenuQueryPairs[j1].split("=");
              if(smenuQueryPair.length == 2) {
                smenuParam[smenuQueryPair[0]] = smenuQueryPair[1];
              }
            }
          }
		      //权限
		      smenuPerm = [];
		      if((bitems = sitem.childList) && bitems.length) {
			      for(k = 0; k < bitems.length; k ++) {
			        bitem = bitems[k];
			        smenuPerm.push({name: bitem["menuNameOrig"], permission: bitem["permission"]});
			      }
		      }
		      //
          pmenu["sub"].push(smenu = {
    			  name: sitem["menuNameOrig"]
    			  , componentName: smenuHref
    			  , params: smenuParam
    			  , icon: sitem["menuIcon"]
            , isShow: sitem["isShow"]
    			  , meta: {
    				  permissions: smenuPerm
    			  }
  			  });
        }
      }
      //console.log(menus);
    },
    error: function(x, s, err) {
      //net::ERR_CONNECTION_REFUSED 
      //DOMException: Failed to execute 'send' on 'XMLHttpRequest': Failed to load
      console.error(err);
    }
  };

  AjaxUtil.send(ajaxOpts);

}

findMenus();
//console.log(menus);
//module.exports = menus;
export default menus;