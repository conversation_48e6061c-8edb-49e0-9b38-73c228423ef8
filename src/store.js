import Vue from 'vue'
import Vuex from 'vuex'

require('es6-promise').polyfill();

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    openTab:[],//所有打开的路由
    activeIndex:'/' //激活状态
  },
  mutations: {
    // 添加tabs
    add_tabs (state, data) {
      //判断路由是否已经打开
      //已经打开的 ，将其置为active
      //未打开的，将其放入队列里
      for (let option of this.state.openTab) {
        if (option.path === data.path || option.name === data.name) {
          return;
        }
      }

      this.state.openTab.push(data);
    },
    // 删除tabs
    delete_tabs (state, route) {
      let index = 0;
      for (let option of state.openTab) {
        if (option.path === route || option.name === route) {
          break;
        }
        index++;
      }
      this.state.openTab.splice(index, 1);
    },
    // 设置当前激活的tab
    set_active_index (state, index) {
      this.state.activeIndex = index;
    },
  },
  actions: {

  }
})