/**
 *
 */
import globalVariables from "@/config/global-config";

import AjaxUtil from "@/utils/AjaxUtil";

var FormHelper = (function() {
  var emptyComboItem = { value: "", label: "所有" };

  /**
   *
   * @param {*} items
   */
  function makeOptions(items) {
    var opts = [];

    if (items && items.length) {
      for (var j = 0, itm, opt, optChd; j < items.length; j++) {
        itm = items[j];
        opts.push((opt = { value: itm["id"], label: itm["dictLabel"] }));
        //
        optChd = makeOptions(itm["children"]);
        if (optChd.length) {
          opt["children"] = optChd;
        }
      }
    }

    return opts;
  }

  var _FormHelper = {
    emptyComboItem: emptyComboItem,
    /**
     *
     * @param {*} dictTypes 字典编码类型
     */
    readCombo: function(callback, dictTypes) {
      var comboItems = {};
      var yr = new Date().getFullYear();
      for (var y = yr; y >= 2018; y--) {
        comboItems["year"].push({ value: y, label: y });
      }

      var ajaxOpts = {
        async: false,
        url: globalVariables.cmis.domain + "/carePlatform/index/readFormCombo",
        dataType: "json",
        success: function(res) {
          if (!res) {
            return;
          }

          for (var nam in res) {
            var fk = "combo_" + nam;
            if (fk in formConfig && nam in res) {
              var items = res[nam];
              var formItems = makeOptions(items);
              formItems.unshift(emptyComboItem);
              formConfig[fk] = formItems;
            }
          }
        }
      };

      AjaxUtil.send(ajaxOpts);
    },

    /**
     * 指定年份的季度首日列表
     * @param byear 首年
     * @param eyear 尾年
     */
    quarterHeadDates: function(byear, eyear) {
      var now = new Date(),
        year = now.getFullYear(),
        qua = Math.floor(now.getMonth()) / 3 + 1;
      if (arguments.length == 0) {
        byear = eyear = year;
      } else if ((arguments.length = 1)) {
        eyear = year;
      }

      var dates = [];
      for (var y = eyear; y >= byear; y--) {
        for (var q = 4, m; q >= 1; q--) {
          if (y > year || (y == year && q > qua)) {
            continue;
          }
          m = 1 + (q - 1) * 3;
          m = m < 9 ? "0" + m : m;
          dates.push({
            value: y + "-" + m + "-01",
            label: y + "年第" + q + "季度"
          });
        }
      }
      //console.log(dates);
      return dates;
    }
  };

  return _FormHelper;
})();

export default FormHelper;
