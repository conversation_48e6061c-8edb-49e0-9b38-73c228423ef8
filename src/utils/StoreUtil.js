
var StoreUtil = (function() {

	const keyDomain = location.host;
	var _uniqKey = function(key) {
	  	return keyDomain + key;
  }
	
	var _StoreUtil = {
 
	  fetch: function(key) {
	 		
	 		var data = window.localStorage.getItem(_uniqKey(key));
			return data? window.JSON.parse(data) : null;
	  },
	 
	  save: function(key, data) {
	 
			window.localStorage.setItem(_uniqKey(key), window.JSON.stringify(data))
	  },
	 
	  clear: function(key) {
	 
			window.localStorage.removeItem(_uniqKey(key))
	  }
	 
	}
	
	return _StoreUtil;
})() 
 
export default StoreUtil