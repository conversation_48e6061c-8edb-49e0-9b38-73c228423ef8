/**
 * 消息队列
 */

import StoreUtil from '@/utils/StoreUtil'
 
var MQUtil = (function(){

  var _CacheMQEKey = "MessageQueueEvent", _CacheMQKey = "MessageQueue";
  //间隔符
  var _DelimComp = "@@", _DelimEvent = "@";
  //
  var _EventPattern = /^([^@]+)\@\@([^@]+)\@([^@]+)$/;
  
  var _Util = {
    
    /**
     * 注册事件
     * @param provider产生消息的组件
     * @param consumer接收消息的组件
     * @param event监听事件
     */
    register: function(provider, consumer, event) {
      var events = StoreUtil.fetch(_CacheMQEKey);
      if(!events) {
        StoreUtil.save(_CacheMQEKey, (events = []));
      }


      //生产消费注册, "生产组件名@@消费组件名@事件名"
      var proConEvt = provider + _DelimComp + consumer + _DelimEvent + event;
      if(-1 != $.inArray(proConEvt, events)) {
        return;
      }
      events.push(proConEvt);
      StoreUtil.save(_CacheMQEKey, events);
      //
      _Util.destroyMessage(provider, consumer, event);
    },
    
    /**
     * 添加
     * @param provider产生消息的组件
     * @param event监听事件
     * @param message消息
     */
    sendMessage: function(provider, event, message){

      var events = StoreUtil.fetch(_CacheMQEKey);
      if(!events || !events.length) {
        return;
      }

      var queue = {};
      for(var i = 0, evt, proConEvt; i < events.length; i ++) {
        evt = events[i];
        proConEvt = _EventPattern.exec(evt);
        //匹配到4个元素第2个是生产组件, 第3个是消费组件, 第4个是事件
        if(!proConEvt || proConEvt.length != 4 || provider != proConEvt[1] || event != proConEvt[3]) {
          continue;
        }
        
        queue[evt] = message;
      }

      var cacheQueue = StoreUtil.fetch(_CacheMQKey);
      if(!cacheQueue) {
        cacheQueue = queue;
      } else {
        $.extend(cacheQueue, queue);
      }

      StoreUtil.save(_CacheMQKey, cacheQueue);
    },
    
    /**
     * 获取事件
     * @param provider产生消息的组件
     * @param consumer接收消息的组件
     * @param event监听事件
     */
    getMessage: function(provider, consumer, event){

      var cacheQueue = StoreUtil.fetch(_CacheMQKey);
      if(!cacheQueue) {
        return null;
      }
      //
      var proConEvt = provider + _DelimComp + consumer + _DelimEvent + event;
      //没有消息
      if(!(proConEvt in cacheQueue)) {
        return null;
      }
      //获取
      var message = cacheQueue[proConEvt];
      //删除消息
      _Util.destroyMessage(provider, consumer, event);

      return message;
    },

    /**
     * 删除消息
     */
    destroyMessage: function(provider, consumer, event) {

      var cacheQueue = StoreUtil.fetch(_CacheMQKey);
      if(!cacheQueue) {
        return null;
      }
      //
      var proConEvt = provider + _DelimComp + consumer + _DelimEvent + event;
      //删除消息
      cacheQueue[proConEvt] = null;
      delete cacheQueue[proConEvt];
      StoreUtil.save(_CacheMQKey, cacheQueue);
    }
  }
  
  return _Util;
  
}) ();

export default MQUtil;

