/**
* 基础工具类
*/
 
var PageUtil = (function(){
	
	var _Util = {
		/**
		 * 浏览器缩放比例
		 */
		browserZoom: function (){ 
			var ratio = 100, screen = window.screen, ua = navigator.userAgent.toLowerCase();
			 
			if (window.devicePixelRatio !== undefined) {
				ratio = window.devicePixelRatio;
			} else if (~ua.indexOf('msie')) {  
				if (screen.deviceXDPI && screen.logicalXDPI) {
					ratio = screen.deviceXDPI / screen.logicalXDPI;
				}
			} else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
				ratio = window.outerWidth / window.innerWidth;
			}
			   
			if (ratio){
				ratio = Math.round(ratio * 100);
			}
			   
			return ratio;
		},
		/**
		 * 主体内容最大高度
		 */
		mainBodyHeight: function() {
		  //
		  /*var height  = //document.documentElement.clientHeight;//h;//
		  //减顶部高度
		  height -= $("#mainHeader").outerHeight();//50;//
		  //减主标签高度
		  height -= $("#mainTabContainer").outerHeight();//41;//
		  //减底部高度
		  height -= $("#mainFooter").outerHeight();//51;//*/
		  
		  var height  = $("#mainBodyContainer").innerHeight();
		  
		  return height;
		},
		
		/**
		 * 计算表格高度, 适用页面只有页标题, 搜索框和表格分页
		 * @param h 最大高度
		 */
		dataTableHeight: function (h) {
		  
		  var height  = _Util.mainBodyHeight();
		  var el;
		  //减当页头高度
		  height -= $("#pageHeader").outerHeight(true);//47;//
		  //减当页搜索框高度
		  //if(_Util.searchFormVisibility(height)) {
			height -= $("#searchFormPane").outerHeight(true) + 2 * 4;//90 + 2 * 4;//
		  //}
		  //减当页分页高度
		  el = $("#dataTablePager");
		  if(el.length) {
			height -= el.outerHeight(true);//32 + 4;//
		  }
		  //减表格父容器空隙
		  height -= 2 * 4;
		  //浏览器缩放
		  /*var zoom = _Util.browserZoom() / 100;
		  height = height + height * (1 - zoom);*/
		  //
		  return Math.max(100, height);
		}
		/**
		 * 搜索表单是否可见, 页面高度小于600时, 不可见
		 * @param h 最大高度
		 */
		, searchFormVisibility: function(h) {
		    var height  = h || document.documentElement.clientHeight;
			
			return height >= 600;
		}
		/**
		 * 强制跳转路由, 如果路由存在就清除, goto(this, {"name": "个人中心"})
		 * @param vm 组件对象
		 * @param route 路由参数
		 */
		, goto: function(vm, route) {
			//删除缓存
			vm.$store.commit("delete_tabs", (route.name || route.path));
			//跳转路由
	    vm.$router.push(route);
		}
	}
	
	return _Util;
	
}) ();

export default PageUtil;

