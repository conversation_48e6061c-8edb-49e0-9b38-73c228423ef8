/**
* 
*/

import LoginHelper from '@/utils/LoginHelper'
 
var AjaxUtil = (function(){
	
	var _AjaxUtil = {
		/**
		 * token请求
		 * @param {*} opts jquery ajax options
		 * @param {*} authIgnore 无需用户登录 
		 */
		send: function(opts, authIgnore){
	
			/*if(!("error" in opts)) {
				opts["error"] = function(req, s, err) {
					console.error(err);
				};
			}*/
			//要求token
			let token = LoginHelper.validateToken();
			if(!token && !authIgnore) {
				return;
			}
			
			var headers = opts['headers'] || {};
			headers['auth-sso-sessionid'] = token;
			
			opts['headers'] = headers;
			
			$.ajax(opts);
		},

		get: function(url, handleSuccess, handleError) {
      var ajaxOpts = {
        type: "get",
        url: url,
        dataType: "json",
        success: handleSuccess || $.noop,
        error: handleError || $.noop
      };

			_AjaxUtil.send(ajaxOpts);
		},

		post: function(url, data, handleSuccess, handleError) {
      var ajaxOpts = {
        type: "post",
        url: url,
        data: data,
        dataType: "json",
        success: handleSuccess || $.noop,
        error: handleError || $.noop
      };

			_AjaxUtil.send(ajaxOpts);
		}
	}
	
	return _AjaxUtil;
	
}) ();

export default AjaxUtil;

