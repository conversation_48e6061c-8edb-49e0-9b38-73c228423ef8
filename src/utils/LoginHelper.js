/**
* 
*/
import global from '@/config/global-config'
import StoreUtil from '@/utils/StoreUtil'
import AjaxUtil from '@/utils/AjaxUtil'
 
var Login = (function(){
	
	/**
	 * 登陆框访问地址
	 */
	const tokenKey = 'token-key', contextPath = '/', loginPath = '/login';
	//用户信息, 用户信息最后读取时间, 用户信息读取间隔毫秒
	var userInfo, userInfoUpdated, userInfoUpdatedInterval = 1 * 1000;;
	
	var _Login = {
		/**
		 * 获取token
		 */
		validateToken: function(){
			return StoreUtil.fetch(tokenKey);
		},
		
		/**
		 * 是否登陆
		 */
		requireAuth: function(name, path){
			//console.log("登录认证, name=" + name +", path=" + path);
			//不需要权限的路由/页面
			if(name && /^(?:登录|平台首页|注册须知|用户信息填写|医院信息填写|重置密码)$/.test(name) 
				|| path && /^\/(?:login|platformIndex|platformRegisterGuide|platformRegisterUser|platformRegisterOrg|resetPassword)?$/.test(path)) {
				return false;
			}
			//是否有token
			let token = StoreUtil.fetch(tokenKey);
			//console.log("token=" + token);
			if(!token || 0 == token.length) {
				return true;
			}
			//读取登录信息
			var uinfo = _Login.logincheck();
			if(!uinfo || !uinfo["userName"]) {
				return true;
			}

			return false; 
		},
		
		/**
		 * 登陆页面
		 */
		login: function() {
			this.$router.push(loginPath)
		},
		
		/**
		 * 登陆验证
		 * @param principal 登录参数, 用户名, 密码, 验证码
		 * @param handleSuccess 登录成功调用函数
		 * @param handleError 登录失败调用函数
		 */
		auth: function(principal, handleSuccess, handleError) {

			var username = principal.username;
			var password = principal.password;
			var verifyCodeId = principal.verifyCodeId;
			var verifyCode = principal.verifyCode;

			var ajaxOpts = {
				url: global.sso.domain + '/app/login'
				, type: 'post'
				, dataType: 'json'
				, data: {
					username: username
					, password: password
					, verifyCodeId: verifyCodeId
					, verifyCode: verifyCode
				}
				, success: function(res) {
					
					if(!res || !res.code) {
						return;
					}
					
					var resCode = res.code;
					switch(resCode) {
						case 200:
							//本地存储token
							StoreUtil.save(tokenKey, res.data);
							if('function' == $.type(handleSuccess)) {
								handleSuccess();
							}
						
							break;
						default:
							//alert(res.msg);
							if('function' == $.type(handleError)) {
								handleError(res.code, res.msg);
							}
									
							break;
					}
				}, error: function(x, s, err) {
					if('function' == $.type(handleError)) {
						handleError(err);
					}
			
				}
			};
			
			$.ajax(ajaxOpts);
		},
		
		/**
		 * 登出
		 */
		logout: function(router) {
			var token = StoreUtil.fetch(tokenKey);
			
			userInfo = null;

			var ajaxOpts = {
				url: global.sso.domain + '/app/logout'
				, type: 'post'
				, dataType: 'json'
				, data: {'sessionId': token}
				, complete: function(x, s) {
					if('success' != s) {
						console.error(x);
					}
			
					StoreUtil.clear(tokenKey);
					
					if(router) {
						router.push(loginPath);
					} else {
						location.href = location.protocol + "//" + location.host + contextPath;
					}
					
				}
			};
			
			$.ajax(ajaxOpts);
		},
		/**
		 * 用户信息
		 */
		logincheck: function(opts) {
			opts = opts || {};
			//附加信息
			var attach = opts["attach"];
			attach = attach? attach.split(",") : attach;
			//间隔时间内, 去最近获取的信息, 减少请求
			if(!!userInfoUpdated && !!userInfo && (new Date().getTime() - userInfoUpdated) < userInfoUpdatedInterval) {
				//是否使用最近获取的信息
				var reuse = true;
				//最近信息是否包含附加的信息
				if((!!attach && attach.length > 0)) {
					for(var i = 0; i < attach.length; i ++) {
						if(!(attach[i] in userInfo)) {
							reuse = false;
							break;
						}
					}
				}
				//
				if(reuse) {
					//console.log(userInfo);
					return userInfo;
				}
			}
			//var userInfo = null;
			var postData = opts || null;
			var ajaxOpts = {
				url: global.cmis.domain + '/sso/auth/getUserInfo'
				, type: 'post'
				, dataType: 'json'
				, data: postData
				, async: false
				, success: function(res) {
					if("true" == res.result) {
						userInfo = res['data'];
						if(userInfo) {
							//头像
							var da = userInfo["avatarUrl"];
							if(da) {
								da = da.replace("/ctxPath", "");
								if(-1 == da.indexOf(global.cmis.domain)) {
									da = global.cmis.domain + da;
								}
								userInfo["avatarUrl"] = da;
							}
							//附加信息
							if(!!attach && attach.length > 0) {
								for(var i = 0, att; i < attach.length && !!(att = attach[i]); i ++) {
									if(att in res) {
										userInfo[att] = res[att];
									}
								}
							}
							//
							userInfoUpdated = new Date().getTime();
						}

						return;
					}
					
					//alert(res.msg);
					console.error(res.msg);
					//console.log(this);
					_Login.logout();
				}
			};
			
			AjaxUtil.send(ajaxOpts);
			
			return userInfo;
		}
	}
	
	return _Login;
	
}) ();

export default Login;

