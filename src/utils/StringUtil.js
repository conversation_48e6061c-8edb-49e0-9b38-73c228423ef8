/**
 * 日期处理工具类
 */

var StringUtil = (function () {

  var defaultPattern = "yyyy-MM-dd HH:mm:ss";

  var _StringUtil = {

    /**
     * 取得数组最后一个值
     */
    getLastNode: function (StringList) {
      var lastNode =
        StringList && StringList.length ?
        StringList[StringList.length - 1] :
        "";
      return lastNode;
    },
    getTreeNames:function(mapInfo,key)
    {
        if(mapInfo.hasOwnProperty(key))
          return mapInfo[key].treeNames;
        return "";
    },
    getIdsByName: function (mapInfo, mapName, dictName) {
      var cityId;
      if (mapName == null || mapInfo == null) return;

      var ids = mapName[dictName];
      var parents = mapInfo[ids.id].parentCodes;
      var pArr = parents.split(',');
      var rets = [];
      if ("0" == pArr[0]) {
        rets = pArr.slice(1, -1);
      }
      rets.push(ids.id);
      return rets;
    },
    getIdsById: function (mapInfo, dictId) {
      if (mapInfo == null || !dictId) return;
      if ("object" == typeof (dictId)) {
        return dictId;
      }
      dictId = dictId + "";
      var dict = mapInfo[dictId];
      if(!dict) {
        return null;
      }
      var parents = dict.parentCodes;
      var pArr = parents.split(',');
      var rets = [];
      if ("0" == pArr[0]) {
        rets = pArr.slice(1, -1);
      }
      rets.push(dictId);
      return rets;
    },

    randomString: function (len) {
  　　len = len || 32;
  　　var Chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
  　　var maxlen = Chars.length;
  　　var str = '';
  　　for (var i = 0; i < len; i++) {
        str += Chars.charAt(Math.floor(Math.random() * maxlen));
  　　}
  　　return str;
    }

  }

  return _StringUtil;

})();

export default StringUtil;
